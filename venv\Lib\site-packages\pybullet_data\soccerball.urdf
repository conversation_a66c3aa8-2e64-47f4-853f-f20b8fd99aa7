<?xml version="1.0" ?>
<robot name="urdf_robot">
  <link name="baseLink">
  	<contact>
      <rolling_friction value="0.03"/>
      <spinning_friction value="0.03"/>
    </contact>

    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value="10.0"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
				<mesh filename="soccerball.obj" scale="1 1 1"/>      
      </geometry>
      <material name="white">
        <color rgba="1 1 1 1"/>
        <specular rgb="11 1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
	 <sphere radius="0.5"/>
      </geometry>
    </collision>
  </link>
</robot>

