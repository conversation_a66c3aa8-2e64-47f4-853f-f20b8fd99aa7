{"metadata": {"num_examples": 1000, "generated_at": 1755484914.6381278, "format": "text_only"}, "examples": [{"id": "example_81ff524c", "text": "drop a 3.6kg sphere in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_a51b8206", "text": "put a 1.9kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_4bfea493", "text": "Build a slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_04b0d68b", "text": "Set up a inclined plane with a ball that will roll down", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_26aae0fc", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_c706996e", "text": "create a sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_d6b318de", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_d2eabfe8", "text": "create a ramp and set a orb on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_4550f717", "text": "create a slope and put a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_de0327d4", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_37f941e0", "text": "create a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e4d03525", "text": "construct a ramp and put a 2.2kg icy sphere at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_75b8afe3", "text": "set a 1.1kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e06ab7a4", "text": "Build a ramp", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_1910176d", "text": "form a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e4ada39f", "text": "put a 1.3kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_0c0f504f", "text": "construct a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_78585049", "text": "set a 0.6kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_0d30101f", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_51a03e6b", "text": "include a super elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_c2cdb987", "text": "create a inclined plane and put a 0.6kg elastic sphere at the top so it will slide down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_fd887948", "text": "introduce a rubber round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_cce6c7d0", "text": "introduce a bouncy orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_21b433a0", "text": "put a 1.4kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_4150314f", "text": "create a orb", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_1f0e8652", "text": "place a 1.5kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_93d66be4", "text": "insert a polymer rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_b1e8dee4", "text": "put a 3.7kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_01111ce5", "text": "introduce a timber ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_6782bda6", "text": "drop a 3.2kg ball in the scene", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_95c04516", "text": "drop a 2.4kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_7c957174", "text": "place a 0.6kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_c6985d21", "text": "form a ramp and set a ball on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_2345cd4c", "text": "include a bouncy orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_c22769bd", "text": "insert a rubber orb that will rebound off a plastic box", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_3a9d8481", "text": "introduce a rubber ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_79d5a44d", "text": "form a ramp and put a ball on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_38b97bdf", "text": "place a 1.7kg round object on top of a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_855e776f", "text": "Build a inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_f5e76c74", "text": "build a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_b2369813", "text": "build a incline and position a 0.5kg polymer ball at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_bc8e1dd2", "text": "create a rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_2c5ea0fc", "text": "Set up a ramp with a sphere that will tumble down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_49c14128", "text": "insert a bouncy sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_0b35f5b3", "text": "introduce a elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_aff73655", "text": "include a wood rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_44cdadeb", "text": "place a 2.4kg ball on top of a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_0f55b61a", "text": "form a slope and drop a round object on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_0c711dc2", "text": "insert a bouncy round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_bf44a3de", "text": "position a 0.9kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_2ab94f5d", "text": "add a super elastic ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_5653cdb0", "text": "Build a inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_967d702a", "text": "create a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_c6925c30", "text": "introduce a marble round object and a wood incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_35ab8828", "text": "Build a physics scene with a inclined plane, a 2.3kg sphere, and a cube that the sphere will hit", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_fdbc911b", "text": "form a sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_12872f46", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_28eeb629", "text": "include a wooden round object that will spring back off a metallic block", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_10672019", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ff6c1c7d", "text": "form a inclined plane and position a 1.4kg rubber round object at the top so it will slide down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_5017c19a", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_65283645", "text": "make a sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_54661a5c", "text": "insert a bouncy round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_f1921d0c", "text": "construct a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_a4775adc", "text": "Set up a slope with a sphere that will roll down", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_20ecf9db", "text": "build a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_ac198033", "text": "place a 3.1kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_4e338a5a", "text": "create a sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_4c1776ac", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_f3c24e45", "text": "Build a inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_110bfbbe", "text": "make a ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_cd78d497", "text": "put a 0.6kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_094ed2fb", "text": "add a elastic ball", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_5d25b363", "text": "form a block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_0fd9aca0", "text": "insert a rubber orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_bc7887d3", "text": "set a 4.3kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_818596d1", "text": "Build a inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_3d017cc5", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_40f7337e", "text": "build a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_abc9fc9a", "text": "set a 0.5kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_5d8150ca", "text": "place a 0.3kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_8b6a117f", "text": "create a sphere", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_194bfe59", "text": "place a 1.1kg sphere in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_0e7511ce", "text": "place a 1.0kg sphere on top of a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_55f8110a", "text": "position a 2.1kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_0a1910d2", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_df82b9d5", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_8e49b98b", "text": "create a cube and a sphere next to each other", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_f0f28a28", "text": "put a 0.5kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_677859dd", "text": "create a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_26c0c156", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_cb48c696", "text": "create a block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_2185490d", "text": "insert a highly elastic round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_decea8f3", "text": "introduce a bouncy ball that will spring back off a rocky block", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_94efcad9", "text": "make a box", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_a136e0b9", "text": "put a 2.8kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_31363be9", "text": "make a incline and place a round object on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_9631e2e8", "text": "Build a inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_5e143adf", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_2a76e435", "text": "place a 1.2kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_9f97bcb0", "text": "set a 3.0kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_c14b3336", "text": "build a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_93e76588", "text": "construct a slope and place a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_81e2891a", "text": "make a block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_f9b88921", "text": "include a wooden ball and a timber ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_c788a760", "text": "construct a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e54a09e8", "text": "introduce a granite ball and a wood inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_95c3268b", "text": "create a scenario where a 3.3kg round object rolls down a wood slope and collides with a block", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_6b79daec", "text": "form a slope and position a ball on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_63cd14b4", "text": "build a rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_4fc3a7cf", "text": "insert a metallic round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_19370d73", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_73085402", "text": "put a 1.1kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_3a593ca7", "text": "Build a inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_968c3054", "text": "create a ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_d6d29baa", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_2b4c6075", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_86512ba3", "text": "drop a 1.2kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_78363958", "text": "build a slope and put a orb on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_739ed250", "text": "place a 0.8kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_c1ff071c", "text": "build a sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_6adbcc0c", "text": "add a bouncy round object and a wooden inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_3929ba40", "text": "build a inclined plane and drop a 1.0kg timber ball at the top so it will tumble down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_4385a555", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_bd8bdca8", "text": "place a 0.5kg ball on top of a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_e2801274", "text": "insert a bouncy box", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_fcdab35e", "text": "Set up a ramp with a sphere that will move down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_6f05c411", "text": "include a plastic ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_65937f05", "text": "form a box", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_e5e67958", "text": "include a wooden cube", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_65027244", "text": "set a 1.3kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_3375aaea", "text": "insert a rubber sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_3c475daf", "text": "introduce a rubber ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_dbeb8d44", "text": "place a 1.9kg sphere on top of a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_0e6a0577", "text": "include a metal round object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_ecf6e2f6", "text": "introduce a wood block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_a52c53c8", "text": "create a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_5e7db6b0", "text": "place a 0.7kg round object on top of a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_a988cef3", "text": "add a rubber round object that will rebound off a polymer cube", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_d28c73ea", "text": "make a block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_6f8b17cd", "text": "put a 0.8kg round object in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_e24eafa0", "text": "make a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_60ee5c08", "text": "set a 3.8kg sphere in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_f12de8b6", "text": "insert a wooden ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_763b9ea8", "text": "insert a bouncy orb and a wood inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_c30c0ea5", "text": "Build a slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_7bd4df66", "text": "include a bouncy orb and a wood incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_0c042f23", "text": "position a 0.8kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_db170372", "text": "add a wood sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_b608a551", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_fd2081d5", "text": "include a rubber ball and a timber incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_3837ba64", "text": "build a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_80cac3c9", "text": "place a 1.2kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_fe2ad99f", "text": "Set up a inclined plane with a ball that will move down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_9a4fed77", "text": "construct a block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_3cf56542", "text": "place a 1.2kg orb in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_f579b421", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_e4c9901a", "text": "insert a metallic orb", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_4b079c21", "text": "Set up a incline with a orb that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_83a709b9", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_1475eee4", "text": "put a 1.7kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_f2405033", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_68838dfb", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_a2c45793", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_4c4a479f", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_cc7cdf0f", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_f8255a8b", "text": "introduce a rubber ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_4ffb1d00", "text": "make a ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_57b273b9", "text": "build a incline and drop a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_4ab63c5d", "text": "include a wood box", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_ccbdb88f", "text": "include a wood round object and a wood inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_dd1b11d1", "text": "drop a 1.5kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_2bea474e", "text": "include a timber orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_9c687426", "text": "create a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_a74738e9", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_d4c24b98", "text": "insert a glass cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_d293b58c", "text": "Set up a slope with a sphere that will roll down", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_d9e0b6f5", "text": "form a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_257229b9", "text": "introduce a polymer orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_487d0fc5", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_eade3be7", "text": "set a 2.7kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_18444699", "text": "construct a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_69fd00ce", "text": "introduce a wood sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_f95bac43", "text": "include a transparent ball and a wooden inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_29bdc6c6", "text": "build a sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_61b23e83", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_5114c8f5", "text": "set a 0.6kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_ac686044", "text": "introduce a timber orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_5964c14f", "text": "create a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_084847d0", "text": "put a 0.4kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_b4777434", "text": "create a incline and position a 2.1kg plastic orb at the top so it will tumble down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_0572c790", "text": "form a ramp and drop a orb on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ec3dbcac", "text": "add a super elastic block", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_c861162a", "text": "add a wooden round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_b3f194c8", "text": "position a 1.4kg round object in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_ca0be1b6", "text": "add a polymer ball", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_3fb7c736", "text": "insert a steel cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_921e8bf2", "text": "add a wooden cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_95cd5d17", "text": "create a inclined plane and position a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_ea094546", "text": "create a block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_4cebc573", "text": "build a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_6276c9ff", "text": "create a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_84ba5596", "text": "construct a sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_bc53e321", "text": "place a 1.9kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_7b7e024b", "text": "build a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_47bba910", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_767b1b16", "text": "place a 0.7kg ball in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_853c8240", "text": "include a rubber sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_e441d09c", "text": "drop a 1.2kg ball on top of a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_1a9a5831", "text": "make a sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_044a671b", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_b93c02b6", "text": "place a 0.6kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_944deba4", "text": "insert a bouncy round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_844dcdf1", "text": "create a inclined plane and drop a orb on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_0fe4ef90", "text": "drop a 3.9kg orb on top of a inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_eb192895", "text": "construct a sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_56d70ea7", "text": "add a highly elastic round object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_387cd327", "text": "create a rectangular object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_8fe6d5d0", "text": "introduce a glassy sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_5b3faefa", "text": "introduce a plastic sphere that will spring back off a plastic box", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_7fd65bae", "text": "Set up a slope with a sphere that will move down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ff4034e9", "text": "build a ramp and put a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_af15a5e3", "text": "create a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_a7439850", "text": "form a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_cfd43312", "text": "add a frozen sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_1b79658e", "text": "include a metal rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_2c816352", "text": "introduce a rocky ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_ac48a19c", "text": "add a elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_08ccae4d", "text": "add a rubber sphere and a wood inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_33a20cd3", "text": "form a inclined plane and set a 0.5kg timber ball at the top so it will slide down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_545c315d", "text": "put a 3.0kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_61c8eb32", "text": "Set up a ramp with a orb that will move down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_4bde8d36", "text": "Set up a slope with a orb that will roll down", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_28fe0c5e", "text": "set a 0.7kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_5d3f69b6", "text": "drop a 1.1kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_58e0f54c", "text": "introduce a bouncy orb that will rebound off a plastic block", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_5f768597", "text": "create a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_71c24fb9", "text": "add a wooden ball", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_9d240516", "text": "position a 2.5kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_79f9a757", "text": "add a super elastic sphere that will bounce off a timber box", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_b0488c8d", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_c8ee8dcc", "text": "place a 1.1kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_6a99aef9", "text": "include a metallic round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_c4ab1375", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_e7a3bfd7", "text": "include a rubber ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_ec6df559", "text": "add a bouncy orb and a wood ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_c23babd3", "text": "set a 1.3kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_ea697fb6", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_ea890fd9", "text": "Build a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_a58a3cac", "text": "build a sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_7cfead76", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_b1e7254d", "text": "construct a incline and drop a 2.8kg rubber sphere at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_770e9965", "text": "add a granite ball that will bounce off a slippery box", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_2b94046f", "text": "create a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_82104e6a", "text": "add a plastic orb", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_81d4ab87", "text": "position a 1.0kg sphere on top of a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_81cdd499", "text": "include a elastic ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_ef9aca03", "text": "introduce a highly elastic ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_fe9501b3", "text": "insert a highly elastic ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_074b0867", "text": "drop a 1.9kg round object on top of a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_d2e6eac5", "text": "add a iron sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_4cce8cbe", "text": "build a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_419eaeb6", "text": "insert a glassy rectangular object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_a58b3da8", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_1214d0cb", "text": "Build a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_af3f4160", "text": "include a polymer sphere and a timber ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_162ca752", "text": "form a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_dcae41cc", "text": "put a 3.1kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_90530e06", "text": "introduce a wooden ball and a wooden inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_119524c6", "text": "drop a 1.2kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_c7cac112", "text": "Build a slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_605d3c60", "text": "build a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_0605fe77", "text": "create a ramp and place a orb on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_e381e2e9", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_45a84862", "text": "construct a incline and position a 2.0kg wooden ball at the top so it will slide down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_6b0f9c49", "text": "include a elastic cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_33430e18", "text": "insert a super elastic round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_a4c91342", "text": "create a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_5e77a9c5", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_55d754dc", "text": "place a 1.0kg sphere in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_8ccc65e3", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_b887b8b7", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_38c252bf", "text": "make a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_7c761265", "text": "make a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_1d8ab046", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_fa0e13c6", "text": "create a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_c78c0b04", "text": "create a rectangular object and a orb next to each other", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_fa0fda43", "text": "form a sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_230ff373", "text": "set a 1.0kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_c2cc7f90", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_72f4cb08", "text": "build a ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_78306d79", "text": "set a 1.9kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_c6be5093", "text": "add a wooden orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_a97c7023", "text": "create a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_0556f037", "text": "add a iron orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_6bcbbf09", "text": "build a box and a round object next to each other", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_ed3be27b", "text": "introduce a icy round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_fc8a4917", "text": "put a 0.8kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_cf7f51e9", "text": "create a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_2ffbf9d5", "text": "place a 1.0kg orb on top of a inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_13c16a95", "text": "position a 2.9kg round object in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_253fed80", "text": "insert a bouncy round object", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_9fe81863", "text": "build a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e6cb7ef3", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_95d318fa", "text": "add a highly elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_cd974339", "text": "insert a plastic block", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_d0f8b836", "text": "build a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_4c9be39b", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_d54a36a8", "text": "create a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_b5614dba", "text": "build a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_571a5191", "text": "add a wood ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_0f1c5892", "text": "insert a bouncy ball", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_496ea275", "text": "place a 4.4kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_cd645618", "text": "put a 1.8kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_db792812", "text": "create a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_c43e3f7b", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_82511f53", "text": "create a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_f68f9c37", "text": "include a highly elastic sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_c6371ce2", "text": "make a cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_41ad139d", "text": "include a metal orb and a timber inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_c3238a4b", "text": "place a 1.4kg orb on top of a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_749ce15d", "text": "put a 0.9kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_0c44434f", "text": "build a block", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_f15b11e1", "text": "introduce a wooden sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_593e2253", "text": "introduce a plastic orb that will bounce off a metallic block", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_afa36d3b", "text": "construct a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_2fc044e3", "text": "build a round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_bb843fca", "text": "introduce a metal sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_bd493dd4", "text": "add a rubber round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_567e2bd5", "text": "introduce a elastic round object and a wood inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_00991f54", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_32f4d7be", "text": "form a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_43b75133", "text": "Set up a incline with a sphere that will slide down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_bb6fb1d5", "text": "build a inclined plane and set a 1.0kg steel round object at the top so it will slide down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_10dc9d06", "text": "insert a glassy sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_8ab2db8a", "text": "create a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_7641add3", "text": "drop a 0.8kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_129d9253", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_1f8cb895", "text": "Set up a ramp with a ball that will slide down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_eb3711c3", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_4d5cc2be", "text": "build a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_11be0eca", "text": "set a 1.3kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_204bcaa5", "text": "include a plastic sphere and a timber ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_b6bfb84e", "text": "insert a bouncy box", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_7f655b5c", "text": "construct a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_29cab02e", "text": "set a 1.6kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_44f3e020", "text": "Set up a ramp with a sphere that will move down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_6152fc94", "text": "add a super elastic orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_94bda9f3", "text": "place a 0.9kg ball on top of a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_bdea848e", "text": "make a block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_9cdf6cbb", "text": "include a rubber ball", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_9418ae5c", "text": "construct a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_4f5dfcd9", "text": "construct a incline and set a 2.7kg glassy sphere at the top so it will slide down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_572caa96", "text": "Set up a slope with a sphere that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_7375da77", "text": "Set up a incline with a sphere that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_4ffdf938", "text": "drop a 1.9kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_60354340", "text": "build a round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_7310dc75", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_1936e7d2", "text": "set a 2.3kg round object in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_b54075d0", "text": "form a ramp and position a round object on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_108138a5", "text": "introduce a stone sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_42ed5e8b", "text": "add a highly elastic ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_aef6c6a2", "text": "insert a wooden rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_b8f8ecae", "text": "place a 1.1kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_2d75a0bb", "text": "drop a 4.6kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_8beb40ca", "text": "make a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_7039f5d4", "text": "introduce a polymer sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_4f7a44fe", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_9a499290", "text": "create a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_b22491a8", "text": "make a box and a sphere next to each other", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_c67b8d96", "text": "drop a 1.3kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_6c2dd01f", "text": "build a round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_eb7dabb5", "text": "create a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_0d652897", "text": "put a 0.5kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_f91f877c", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_19f8cec5", "text": "set a 0.9kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_8cba38b0", "text": "Set up a inclined plane with a round object that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_90b812aa", "text": "drop a 0.8kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_bf307fdf", "text": "place a 1.9kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_550c5434", "text": "add a iron round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_416c5f8a", "text": "add a wooden ball and a wooden slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ed6e36ba", "text": "make a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_6dc2601a", "text": "build a inclined plane and put a round object on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_1ca31849", "text": "include a rubber orb", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_cac18a5b", "text": "make a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_93514dda", "text": "make a slope and drop a 3.6kg frozen ball at the top so it will tumble down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_5db7971c", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_dfa8b431", "text": "add a rubber round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_e199ecb2", "text": "introduce a highly elastic sphere and a wooden inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_83d920cd", "text": "Build a physics scene with a inclined plane, a 2.1kg sphere, and a cube that the sphere will hit", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_5afd1b80", "text": "introduce a stone sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_31f9b617", "text": "Build a slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_19fda1ff", "text": "introduce a rubber orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_d7fa0a82", "text": "add a bouncy sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_e0776177", "text": "Build a physics scene with a incline, a 3.6kg orb, and a box that the orb will hit", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_ad1ec93f", "text": "position a 1.5kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_2f416645", "text": "create a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_70daf723", "text": "build a ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_7f565fda", "text": "make a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_63fca9a0", "text": "create a scenario where a 1.4kg round object rolls down a wood slope and collides with a cube", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_bb28d7be", "text": "insert a elastic ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_be7de51e", "text": "form a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_2946c260", "text": "make a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_4f752793", "text": "drop a 4.5kg round object in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_8b1e827a", "text": "position a 2.2kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_1accaf74", "text": "create a round object", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_2ac1891f", "text": "form a cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_8b399e8d", "text": "form a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_d09ec9b5", "text": "make a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_8b135650", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_5b311110", "text": "construct a scenario where a 2.4kg sphere rolls down a wooden ramp and collides with a rectangular object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_eb6b5be1", "text": "insert a plastic round object", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_cf8fe5ef", "text": "make a ramp and place a 1.6kg plastic round object at the top so it will roll down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_1ba0dcbb", "text": "include a bouncy round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_df85ee2c", "text": "insert a bouncy orb", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_75943039", "text": "insert a highly elastic sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_e342becf", "text": "construct a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_83e33730", "text": "Build a physics scene with a ramp, a 1.8kg orb, and a block that the orb will hit", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_e2f4107d", "text": "build a sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_22054bc9", "text": "place a 1.3kg orb on top of a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_91748124", "text": "insert a timber rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_edd77182", "text": "create a incline and place a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_da56821a", "text": "add a rubber ball", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_0d934e08", "text": "create a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_9a4a9a41", "text": "add a granite sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_f8736e7c", "text": "set a 1.5kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_071c5b47", "text": "drop a 0.8kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_71d5612b", "text": "place a 2.3kg ball in the scene", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_d76d7097", "text": "construct a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_440159c3", "text": "build a block and a sphere next to each other", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_375c4ba0", "text": "insert a polymer ball that will bounce off a stone rectangular object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_9e311579", "text": "place a 3.8kg sphere on top of a ramp", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_868bf8d2", "text": "add a wood ball that will spring back off a timber block", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_72c06e2e", "text": "create a slope and position a ball on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_3e37ada1", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_afe85bb6", "text": "drop a 0.6kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_07f0b5e8", "text": "introduce a steel orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_7cad3d83", "text": "introduce a wooden orb that will bounce off a rocky cube", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_006e27c9", "text": "include a polymer orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_d7949f6c", "text": "make a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_56cf270e", "text": "form a slope and drop a round object on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_ffd290c6", "text": "add a super elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_3894f87e", "text": "construct a inclined plane and drop a 1.2kg wood round object at the top so it will slide down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_20302856", "text": "add a metallic rectangular object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_8bd0a9d3", "text": "form a inclined plane and drop a 1.2kg steel sphere at the top so it will roll down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_93c21422", "text": "set a 0.6kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_7648b405", "text": "Set up a slope with a round object that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_b45251e4", "text": "add a wooden sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_328db60e", "text": "insert a elastic round object that will spring back off a wooden cube", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_8bb8dab2", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_71a6ff8c", "text": "create a slope and place a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_b70664ae", "text": "put a 0.8kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_755d7e71", "text": "include a metallic ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_8042e8aa", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_f642fb46", "text": "create a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_90f5c82d", "text": "create a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_3d70108a", "text": "include a elastic ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_115c155a", "text": "create a round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_bb2b9aa1", "text": "drop a 3.4kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_1594c92d", "text": "create a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_769e571f", "text": "set a 0.7kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_82641394", "text": "create a incline and set a 1.7kg glass ball at the top so it will tumble down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_b6459a95", "text": "introduce a wooden orb and a wooden slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_2e949903", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_dfda74a4", "text": "introduce a stone orb", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_d1e2644a", "text": "position a 4.9kg round object in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_bfe70dc1", "text": "place a 0.8kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_60b62944", "text": "create a box", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_48f8252d", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_04f6ff08", "text": "put a 1.7kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_0eba6d89", "text": "place a 1.8kg orb in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ed7a3ed8", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_da2d4416", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_d4ac9af1", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_ea554165", "text": "create a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_2866189c", "text": "form a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_ce7e6fe8", "text": "insert a rubber sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_59cb9fe3", "text": "include a rubber ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_ea6adaf9", "text": "include a plastic round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_7e9d9fab", "text": "insert a wood block", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_a66b5f15", "text": "create a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_e71c2f21", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_2a56bfb7", "text": "construct a inclined plane and drop a 1.8kg metallic ball at the top so it will move down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_da040b03", "text": "form a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_19a00e3a", "text": "insert a bouncy sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_223a6adf", "text": "create a incline and drop a 0.7kg plastic sphere at the top so it will slide down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_7f653b89", "text": "insert a bouncy orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_ec73255e", "text": "include a wooden rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_1752fe90", "text": "form a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_d6d4acd8", "text": "insert a elastic ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_826d46ca", "text": "construct a ramp and position a 3.0kg rubber ball at the top so it will roll down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_5a67de80", "text": "construct a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_3310b9c6", "text": "insert a iron orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_d62a00b1", "text": "place a 1.0kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_55d52f4b", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_86d1c63c", "text": "introduce a wood orb", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_fd3b6007", "text": "build a slope and put a 1.9kg wood orb at the top so it will slide down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_063c6538", "text": "insert a wood orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_ec31f54a", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_f2796281", "text": "set a 0.9kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_593e22e5", "text": "add a highly elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_b1013364", "text": "introduce a wooden ball and a wooden ramp", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_579d31c4", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_65537ab8", "text": "add a timber ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_030ccd4a", "text": "Build a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_8468acb9", "text": "introduce a metallic orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_caa3c8ed", "text": "add a polymer round object", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_9f969f0d", "text": "Build a physics scene with a slope, a 1.0kg ball, and a box that the ball will hit", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_9e016c27", "text": "put a 1.4kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_94cc269b", "text": "place a 1.7kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_bc134e99", "text": "create a slope and position a sphere on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_11176f55", "text": "add a wooden orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_a1572ae3", "text": "add a rubber round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_eea8dba7", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_2415fd59", "text": "add a timber ball that will spring back off a rocky block", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_cd708d90", "text": "add a iron round object and a timber incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_42478869", "text": "build a sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_771944e6", "text": "introduce a wooden round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_d580ce51", "text": "include a rubber orb", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_2e66ffbf", "text": "build a rectangular object and a ball next to each other", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_eb45830c", "text": "form a slope and drop a ball on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ead47d60", "text": "make a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_83ff5678", "text": "build a round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_b691bd2f", "text": "place a 1.3kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_c5f91db2", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_8baea5ff", "text": "build a block", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_2da78cc6", "text": "set a 1.5kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_37d9f914", "text": "build a rectangular object and a orb next to each other", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_fe6dd20c", "text": "insert a iron orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_f9d4cf32", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_dc76936c", "text": "construct a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_4526449c", "text": "position a 1.3kg sphere in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_49390fb1", "text": "add a highly elastic ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_153210fe", "text": "create a block and a orb next to each other", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_6ad94c05", "text": "form a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_dcd9dae7", "text": "add a wood orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_fe14adec", "text": "add a elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_fe3c673f", "text": "Set up a incline with a sphere that will slide down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_26af264d", "text": "build a incline and set a orb on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_96c2b46f", "text": "build a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_5d16932f", "text": "build a incline and set a orb on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ce586d43", "text": "Build a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_b4e1133f", "text": "set a 4.9kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_6980652a", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_94044cbf", "text": "build a inclined plane and put a sphere on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_93d8931c", "text": "drop a 0.4kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_fb83df6c", "text": "insert a marble orb and a wood inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_4ce04d9d", "text": "construct a incline and set a 2.1kg rubber ball at the top so it will roll down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_8eba665d", "text": "place a 0.7kg round object on top of a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_4992ca3a", "text": "set a 0.5kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_978166d5", "text": "set a 1.0kg orb in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_456c4695", "text": "Build a physics scene with a incline, a 1.0kg sphere, and a rectangular object that the sphere will hit", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_27f50b99", "text": "position a 1.3kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_40e8a208", "text": "set a 2.9kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_cdd9d32f", "text": "position a 3.0kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_e2170057", "text": "set a 3.0kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_7f38de5e", "text": "place a 2.6kg sphere on top of a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_88fd3c5b", "text": "introduce a timber orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e9b66bf4", "text": "add a polymer ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_fcffeeb4", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_88b30710", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_6b26740e", "text": "build a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_3fe55d5a", "text": "form a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_79c1a8f1", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_b53888bc", "text": "build a slope and set a 1.4kg wooden round object at the top so it will move down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_19bb336e", "text": "Set up a ramp with a sphere that will slide down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_155b05ad", "text": "position a 0.5kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_969cafc4", "text": "create a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_9f851bd4", "text": "make a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_3a9c641f", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_24ece10c", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_1cf99eb8", "text": "make a scenario where a 3.7kg round object rolls down a wooden ramp and collides with a cube", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_0c20aa30", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_b0abf9fb", "text": "include a stone ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_3a1f4ba3", "text": "build a sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_cf128122", "text": "place a 1.9kg sphere in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_50f6b20e", "text": "build a incline and position a ball on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_6d0d282f", "text": "make a rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_e2b237d8", "text": "place a 0.7kg ball in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_f33cdb64", "text": "include a bouncy sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_221eb147", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_d479434a", "text": "create a ramp and put a 2.3kg rocky ball at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_cc7d1e93", "text": "place a 1.8kg orb on top of a ramp", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_e95e952e", "text": "set a 1.4kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_abf1cec1", "text": "add a rubber round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_252ee75e", "text": "drop a 1.7kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e33160e0", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_ba122d45", "text": "add a rubber ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_a7418cd2", "text": "position a 0.8kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_a3e93f83", "text": "construct a scenario where a 2.2kg round object rolls down a wood ramp and collides with a rectangular object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_46e82727", "text": "create a round object", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_0b87f2b4", "text": "construct a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_ef22570e", "text": "position a 1.4kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_afb15f7c", "text": "make a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_7f3a31a6", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_892ddf52", "text": "set a 4.7kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_9937a1f7", "text": "position a 0.8kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_41537b20", "text": "create a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_6c742b02", "text": "position a 4.8kg round object in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_488369dc", "text": "include a plastic round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_8fb749de", "text": "create a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_6402015e", "text": "Build a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_aa31e01b", "text": "insert a elastic ball and a wood incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_63672770", "text": "create a block and a round object next to each other", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_fad9f492", "text": "place a 2.2kg orb on top of a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_1fce6dfb", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_b2bb4fe6", "text": "position a 3.7kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_e97dbf8c", "text": "build a sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_08945b54", "text": "put a 0.6kg sphere in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_40ce7a54", "text": "put a 0.7kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_8071c50c", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_37abc526", "text": "introduce a plastic sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_152d7fb4", "text": "place a 5.0kg round object in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_3144cbc9", "text": "add a granite sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_a00e8abc", "text": "drop a 1.0kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_ea39a06d", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_b8db5bdd", "text": "construct a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_200a0b0e", "text": "add a bouncy round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_aaf0be0c", "text": "place a 1.4kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_a4f81bde", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_87400992", "text": "position a 2.6kg round object in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_ade7b6c8", "text": "make a sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_1e9e92c2", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_66d2e568", "text": "form a ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_a7c88ee5", "text": "drop a 2.7kg ball in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_e1e4ea85", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_24df0e67", "text": "construct a slope and drop a 2.0kg elastic ball at the top so it will move down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_c4f802c3", "text": "introduce a rubber ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_6cba5017", "text": "introduce a wood sphere that will rebound off a glass block", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_ab66da9b", "text": "drop a 2.3kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_3233d2df", "text": "Set up a ramp with a sphere that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_5158baf1", "text": "construct a ramp and place a orb on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_4a206c27", "text": "make a cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_1adb0128", "text": "add a timber sphere and a timber inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_c6fff892", "text": "form a ramp and position a 1.3kg glass sphere at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_3bfebe6e", "text": "put a 4.8kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_3ebfb202", "text": "create a ball", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_f3ef4e42", "text": "create a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_4ce6466e", "text": "Set up a ramp with a sphere that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_1edf4362", "text": "make a ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_2b3f5fe1", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_2742ee13", "text": "introduce a marble orb and a wood slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_e1226146", "text": "place a 0.9kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_00f01808", "text": "include a metal round object", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_a8079be3", "text": "create a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_c43be6f2", "text": "put a 1.0kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_144a8f83", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_9df42290", "text": "create a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_fe7cadc0", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_84ed3e01", "text": "introduce a rocky sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_e31efa6d", "text": "create a box", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_2d1c0db3", "text": "Set up a slope with a sphere that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_5f02e52e", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_056e7ef2", "text": "introduce a glass rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_b12b8bce", "text": "include a bouncy sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_477b1587", "text": "insert a marble orb and a wood inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_bc6bfb5e", "text": "form a ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_d9f5d799", "text": "create a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_5afde62a", "text": "set a 2.3kg round object on top of a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_cfff90de", "text": "construct a incline and place a orb on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_68d41a08", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_c3614f45", "text": "create a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_eaccbafd", "text": "create a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_1990b3ef", "text": "build a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_963bae09", "text": "drop a 2.9kg round object in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_8d225c57", "text": "create a sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_1f1a3485", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_1681b166", "text": "build a ramp and place a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_bbe684ee", "text": "introduce a plastic box", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_63f214db", "text": "place a 1.9kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_411bd603", "text": "introduce a rubber box", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_5c728d36", "text": "form a ramp and place a orb on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_88bc2802", "text": "include a bouncy round object", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_202f434d", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_820763bf", "text": "add a timber round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_b807186c", "text": "make a cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_c7c3d4c9", "text": "insert a polymer sphere and a timber ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_78be4cab", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_954277a4", "text": "set a 1.0kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_9ebb3050", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_d7fc6933", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_f4bf1a62", "text": "set a 1.1kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_43ec3c52", "text": "Build a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_cd6830f6", "text": "drop a 1.0kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_3d15633e", "text": "add a wood orb", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_252c6c7e", "text": "Build a slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_443692c7", "text": "introduce a bouncy block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_697b218b", "text": "put a 1.4kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_5c150b8b", "text": "include a wood ball and a wood ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_f6a63b3f", "text": "introduce a elastic round object that will bounce off a polymer cube", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_2e4b4052", "text": "add a plastic orb and a timber slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_110c91f2", "text": "create a rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_3ed749b6", "text": "construct a inclined plane and place a 1.8kg polymer orb at the top so it will roll down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_db33e665", "text": "add a elastic orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_0e5361f2", "text": "include a metallic orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_dcd65e2b", "text": "drop a 0.7kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_0f1a7913", "text": "Set up a inclined plane with a ball that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_6e62e70d", "text": "set a 2.0kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_2711ded0", "text": "construct a sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_b77358e1", "text": "drop a 1.9kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_13da4d2c", "text": "include a granite box", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_32fc4e67", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_14ae9f6c", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_6a85dfd8", "text": "add a timber orb", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_c6fa1450", "text": "add a highly elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_d2e7c530", "text": "add a highly elastic sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_18b89a94", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_f26a25bc", "text": "put a 1.3kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_89eedea6", "text": "add a metal ball", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_621094ec", "text": "introduce a wood round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_c261f8db", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_ca5c664b", "text": "insert a rubber round object", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_a4963f73", "text": "construct a scenario where a 3.1kg round object rolls down a wooden inclined plane and collides with a rectangular object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_44c3346c", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_fdf98c40", "text": "create a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_3299090b", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_55b68e78", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_9e043ae6", "text": "make a rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_d6f6c403", "text": "form a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_436f3a77", "text": "build a rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_9e524b2a", "text": "Build a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_651d25b2", "text": "drop a 1.4kg sphere on top of a ramp", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_8b67c080", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_cfce0f6e", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_ef2477dc", "text": "place a 2.4kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_d2811de1", "text": "include a metal sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_f5aa4b84", "text": "add a wood box", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_b956a13f", "text": "add a polymer round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_c39fda02", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_c53b1a67", "text": "position a 1.4kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_75904bfc", "text": "construct a inclined plane and place a 0.8kg polymer sphere at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_49e36608", "text": "drop a 1.7kg ball in the scene", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_1001ce45", "text": "construct a incline and drop a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_11a73f32", "text": "insert a wooden sphere and a timber ramp", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_e9b05fc5", "text": "Build a ramp", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_a47f5270", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_e3c1b5ff", "text": "make a cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_32aa7429", "text": "build a incline and position a 2.4kg steel orb at the top so it will tumble down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_0bfcb0c5", "text": "set a 0.7kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_b93a7760", "text": "position a 0.4kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_824a4ca6", "text": "create a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_eecfb89b", "text": "drop a 1.0kg sphere in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_7668d3ad", "text": "introduce a elastic orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_1ee1c623", "text": "introduce a super elastic ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_c43abd64", "text": "add a glass sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_087d5935", "text": "introduce a plastic block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_6f42bf05", "text": "include a wooden box", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_0d46b46d", "text": "position a 4.8kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_7a8ab97a", "text": "drop a 4.7kg round object in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_223f4b35", "text": "Set up a inclined plane with a orb that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_dc8bdef2", "text": "introduce a wood orb and a timber ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_37f9950a", "text": "create a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_0ec2ad45", "text": "place a 0.5kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_827ddbb4", "text": "make a incline and drop a 2.9kg granite ball at the top so it will slide down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_07e16e21", "text": "build a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_6f2fc9b4", "text": "build a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_91e4b6fd", "text": "build a block", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_1937fb1e", "text": "drop a 0.7kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_a90ed2f5", "text": "put a 1.3kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_c7db0c2d", "text": "include a glass round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_6e3148a2", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_374a32a5", "text": "drop a 1.2kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_94229c23", "text": "include a timber orb and a wooden ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_a9b83c18", "text": "Build a slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_24bb16ac", "text": "construct a cube and a ball next to each other", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_2d962353", "text": "create a ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_615dbb8b", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_ad12997d", "text": "place a 4.1kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_2aaf022c", "text": "add a transparent orb", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_ad614ac3", "text": "drop a 2.9kg round object in the scene", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_c488cc9f", "text": "drop a 0.8kg round object on top of a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_267f141e", "text": "construct a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e3c44c09", "text": "drop a 0.5kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_43505c78", "text": "Set up a ramp with a ball that will slide down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_c05bbb9c", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_922c6521", "text": "place a 0.8kg ball in the scene", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_726542c3", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_546c4dda", "text": "introduce a plastic orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_0752beca", "text": "put a 0.9kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_20705a56", "text": "Build a ramp", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_ea28fb12", "text": "set a 1.3kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_810f648e", "text": "drop a 0.7kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e5767e80", "text": "create a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_6fef4b4f", "text": "build a slope and drop a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_2ca149e9", "text": "drop a 1.4kg orb on top of a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_9fbc8d89", "text": "introduce a plastic round object and a wood inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_0e15d45f", "text": "create a scenario where a 1.3kg orb rolls down a timber slope and collides with a box", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_db6716bc", "text": "make a sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_260fb626", "text": "introduce a steel round object and a wooden slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_e2d8d041", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_fd402a5c", "text": "introduce a bouncy orb", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_6f3daf90", "text": "create a box and a ball next to each other", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_790d8bf7", "text": "introduce a bouncy round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_e273af01", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_4c585f11", "text": "place a 2.5kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_615577df", "text": "introduce a plastic round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_29c55eb6", "text": "introduce a plastic sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_2e249195", "text": "create a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_f8c68e25", "text": "Set up a ramp with a ball that will slide down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_cb22030a", "text": "drop a 0.9kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_f5e7cbd3", "text": "build a scenario where a 3.1kg sphere rolls down a timber inclined plane and collides with a box", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_41dea6a4", "text": "insert a bouncy orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_6fc93b73", "text": "set a 1.3kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e5838842", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_114cb339", "text": "place a 1.2kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_09cec9ec", "text": "Build a ramp", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_ebdd04e2", "text": "position a 0.7kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_34bc956f", "text": "build a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_63530950", "text": "introduce a elastic sphere and a wood inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_c4b42e0e", "text": "form a inclined plane and position a 2.9kg metal round object at the top so it will tumble down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_c762a313", "text": "position a 2.7kg sphere in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_6271630c", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_9cd72dd5", "text": "introduce a bouncy sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_7c6441c8", "text": "Build a physics scene with a ramp, a 3.9kg orb, and a cube that the orb will hit", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_a4aaa429", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_4a2ed9fd", "text": "include a timber sphere and a wood ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_20db1b47", "text": "add a icy round object", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_d412bcb4", "text": "include a marble sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_cd0f4aad", "text": "add a rocky orb that will spring back off a wooden box", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_52f1c1b0", "text": "build a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_6814492c", "text": "position a 0.8kg ball in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_423e4cfe", "text": "build a round object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_cb1edb0e", "text": "place a 1.0kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_5d9db42b", "text": "Build a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_63e4631c", "text": "Set up a inclined plane with a orb that will roll down", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_fd069370", "text": "create a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e09fd6a7", "text": "create a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_26b6e299", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_68a95470", "text": "place a 1.7kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_65c2cd72", "text": "make a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_5a520c47", "text": "introduce a wooden block", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_5f882e6f", "text": "add a elastic rectangular object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_81bd8e9a", "text": "create a cube and a orb next to each other", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_86bdb259", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_a4ba2b39", "text": "create a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_1519bc7f", "text": "Build a slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_1e298c5e", "text": "add a elastic cube", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_ae120ac5", "text": "construct a ramp and place a 0.6kg rubber sphere at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_47e926ba", "text": "build a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_a293ae24", "text": "set a 3.8kg round object in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_cb16a925", "text": "add a elastic ball that will bounce off a wooden rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_a8838d8b", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_a57a7318", "text": "add a frozen orb", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_8fd3285c", "text": "set a 1.3kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_9bf2f8ac", "text": "build a cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_6b75ed32", "text": "add a highly elastic sphere and a wooden slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_3330d8cf", "text": "introduce a rocky sphere and a wooden incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_3912bbe4", "text": "Build a slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_f6619920", "text": "place a 0.7kg orb in the scene", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_6e7ac51e", "text": "put a 1.7kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_ae0a593f", "text": "build a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_f14d3f5d", "text": "include a rubber orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_38c18351", "text": "place a 1.1kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_7ad05dbb", "text": "drop a 0.5kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_9bbf86a9", "text": "place a 1.1kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_8d90cf45", "text": "drop a 1.3kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_7df87c6a", "text": "make a box and a sphere next to each other", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_f42032cf", "text": "drop a 2.7kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_30aad671", "text": "place a 2.6kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_4c42ca53", "text": "set a 1.4kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_fa1b28e8", "text": "drop a 1.9kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_be7d650d", "text": "Set up a slope with a orb that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_26c2b2b5", "text": "insert a wood round object and a timber incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_4bfea081", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_76de0931", "text": "introduce a metallic sphere and a timber ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_608ac864", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_7f84ab8d", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_0c7daabe", "text": "build a round object", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_0c8e704a", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_8e824c50", "text": "make a ramp and position a sphere on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_0c4c9fa3", "text": "place a 1.7kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_fe6af076", "text": "set a 1.0kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_0b8398ca", "text": "place a 0.9kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_31d8f8ce", "text": "construct a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_6f1d7f1f", "text": "create a inclined plane and position a 2.2kg rubber orb at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_d4e801e3", "text": "include a timber rectangular object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_9943c814", "text": "insert a ice orb and a timber slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_bbe196a5", "text": "position a 0.5kg ball in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_d1abd0d8", "text": "add a wood orb", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_4674e607", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_2054e8da", "text": "place a 0.6kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_4548028b", "text": "add a plastic sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_701428b8", "text": "make a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_6ebdd508", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_08c03b26", "text": "create a incline and put a ball on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_6963e0a2", "text": "drop a 0.8kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_66d2fe71", "text": "introduce a bouncy box", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_89551997", "text": "include a granite orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_600f52ea", "text": "include a wooden orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_2c0833aa", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_15e3b289", "text": "Build a ramp", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_eb69f5c9", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_85245892", "text": "build a slope and put a 0.6kg rubber round object at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_a81799ee", "text": "include a bouncy sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_e1ca3062", "text": "Build a incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_3adda267", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_089206d1", "text": "create a incline and put a 0.8kg metallic ball at the top so it will roll down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_4bb9c60d", "text": "set a 1.4kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_5d509c82", "text": "build a ramp and drop a 2.4kg rubber sphere at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_09878eb8", "text": "build a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_22898841", "text": "include a elastic ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_92ce870f", "text": "make a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_6642c9af", "text": "include a icy ball and a wood slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_84eb7aba", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_7eb877f2", "text": "include a rubber block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_ade5527b", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_5262b40f", "text": "create a sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_450cca5d", "text": "make a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_49001535", "text": "construct a block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_5559123e", "text": "create a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_960f0943", "text": "build a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ae57405b", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_6b610a7d", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_8f8b7435", "text": "introduce a iron sphere and a wooden ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_84263c0c", "text": "build a box and a round object next to each other", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_e67a0d99", "text": "insert a bouncy orb", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_6bd08591", "text": "include a metallic sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_8798f346", "text": "drop a 1.6kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_fdac56f4", "text": "set a 3.9kg round object in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_a388e64b", "text": "construct a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_7cab06eb", "text": "insert a rubber orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_837f7a3d", "text": "include a rubber orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_9ed067a3", "text": "create a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_3ab52287", "text": "set a 0.7kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_f3a89b53", "text": "drop a 1.0kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_5c82fd60", "text": "set a 1.4kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_7fd6d564", "text": "put a 1.2kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_07731029", "text": "drop a 0.5kg ball in the scene", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_e5438966", "text": "construct a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_e35ce901", "text": "Build a physics scene with a inclined plane, a 1.6kg sphere, and a block that the sphere will hit", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_5f180e6c", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_7c6a561b", "text": "include a glass round object and a wood ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_6a619cc4", "text": "insert a elastic round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_3b86bc5f", "text": "include a wood ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_7fc583d2", "text": "Build a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_aa4b6697", "text": "make a cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_00fc4d17", "text": "Set up a incline with a orb that will roll down", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_e3cbe2bb", "text": "add a elastic ball", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_0ac7a593", "text": "add a elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_4f16856d", "text": "place a 0.9kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_4af98fcb", "text": "introduce a frozen sphere and a wood incline", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_7d17f6a6", "text": "drop a 1.3kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_206b87cd", "text": "add a highly elastic sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_bee1c173", "text": "build a box and a orb next to each other", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_b2153c70", "text": "build a round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_2ddf72c7", "text": "build a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_964ddaf0", "text": "form a inclined plane and place a sphere on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_31174f49", "text": "place a 1.0kg orb in the scene", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_de7c4f88", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_536fab25", "text": "build a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_3f4fb1ec", "text": "Build a slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_beb723a9", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_9d4ea828", "text": "construct a inclined plane and drop a 1.5kg frozen orb at the top so it will slide down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_0225325a", "text": "drop a 0.9kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_2faa2a86", "text": "create a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_e9a66acf", "text": "Build a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_b3b23aeb", "text": "form a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_c97707b5", "text": "build a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_00fffa27", "text": "drop a 1.8kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_0395ab28", "text": "position a 1.3kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_bb71a542", "text": "position a 1.8kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e423cdd5", "text": "insert a glass orb and a timber inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_de5fee19", "text": "place a 1.3kg sphere on top of a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_994eeb0a", "text": "construct a cube and a sphere next to each other", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_5337f414", "text": "set a 1.2kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_591a81d3", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_9802c368", "text": "add a rubber ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_a9f686ce", "text": "introduce a wood ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_debfdfe4", "text": "drop a 0.8kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_0f1fd6ea", "text": "form a ramp and put a 1.2kg wood ball at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_952be06f", "text": "build a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_43553a2b", "text": "create a ramp and set a 1.9kg wood orb at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_c3195a8f", "text": "drop a 0.7kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_fcc560eb", "text": "introduce a wooden round object", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_0ee4ce79", "text": "construct a box", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_95bec0af", "text": "drop a 4.9kg round object in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_fa141b7d", "text": "build a inclined plane and set a ball on it", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_e21a6225", "text": "create a ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ecf5a984", "text": "create a round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_239847f3", "text": "introduce a rubber round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_2f0cb3d7", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_c108df93", "text": "set a 1.9kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_ae641e64", "text": "introduce a timber orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_8e6b77b5", "text": "build a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_21798fcd", "text": "construct a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_9397e870", "text": "form a ramp and place a 2.9kg iron ball at the top so it will slide down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_39cc8fed", "text": "put a 0.4kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_40ce1d43", "text": "position a 2.3kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_b2198e09", "text": "insert a wooden orb", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ad784fc9", "text": "construct a incline and place a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_b972d010", "text": "place a 1.1kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_33ac1aa3", "text": "Build a inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_f8e19f4c", "text": "drop a 1.4kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_5cbbeb38", "text": "form a ramp and drop a 0.8kg polymer orb at the top so it will move down due to gravity", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_a9827a3d", "text": "make a incline and place a ball on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_11a12326", "text": "insert a stone orb", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_dc9aaf3c", "text": "place a 0.8kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_4526d9bd", "text": "introduce a elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_38390e2f", "text": "create a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_b0f79625", "text": "include a elastic ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}]}