{"dataset_evaluation": {"scene_metrics": {"object_count_accuracy": 0.4699999988079071, "object_type_accuracy": {"overall": 0.5440624952316284, "per_type": {"sphere": 0.8671875, "box": 0.4615384638309479, "cylinder": 0.25925925374031067, "ramp": 0.9772727489471436, "cone": 0.30434781312942505}}, "position_error": {"l2_mean": 0.7037228941917419, "l2_std": 0.9352503418922424, "l1_mean": 1.008610725402832, "l1_std": 1.4249546527862549}, "scale_error": {"relative_mean": 15381.056640625, "relative_std": 38717.91015625}, "mass_error": {"relative_mean": 213331.421875, "relative_std": 337783.6875}, "material_accuracy": 0.8212500214576721}, "physics_metrics": {"objects_in_bounds": 1.0, "no_overlaps": 0.8, "stable_configuration": 1.0, "reasonable_masses": 1.0, "reasonable_scales": 1.0, "overall": 0.96}, "num_examples": 200}, "sample_evaluation": [{"text": "create a ball", "predicted_scene": "PhysicsScene(scene_id='test_scene_0', objects=[PhysicsObject(object_id='ground', object_type=<ObjectType.PLANE: 'plane'>, position=Vector3(x=0, y=0, z=0), rotation=Vector3(x=0, y=0, z=0), scale=Vector3(x=10, y=10, z=1), mass=0, material=<MaterialType.STONE: 'stone'>, material_properties=MaterialProperties(friction=0.8, restitution=0.2, density=2700), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.5, 0.5, 0.5)), PhysicsObject(object_id='decoded_obj_0', object_type=<ObjectType.SPHERE: 'sphere'>, position=Vector3(x=-0.2864631116390228, y=-0.10622343420982361, z=1.4817280769348145), rotation=Vector3(x=-0.008605195209383965, y=0.1099390983581543, z=-0.013620186597108841), scale=Vector3(x=1.1432256698608398, y=0.1850675642490387, z=0.4740667939186096), mass=1.2652673721313477, material=<MaterialType.WOOD: 'wood'>, material_properties=MaterialProperties(friction=0.7, restitution=0.5, density=600), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.7, 0.3, 0.3)), PhysicsObject(object_id='decoded_obj_1', object_type=<ObjectType.SPHERE: 'sphere'>, position=Vector3(x=-0.515246570110321, y=0.01431824266910553, z=1.5248472690582275), rotation=Vector3(x=0.008049379102885723, y=-0.0014721062034368515, z=0.011418350040912628), scale=Vector3(x=0.0971025601029396, y=0.097209632396698, z=0.09992320090532303), mass=1.4348571300506592, material=<MaterialType.RUBBER: 'rubber'>, material_properties=MaterialProperties(friction=0.8, restitution=0.9, density=1200), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.7, 0.3, 0.3))], environment=SceneEnvironment(gravity=Vector3(x=-0.0008896887302398682, y=-0.0017647035419940948, z=-9.645936965942383), air_resistance=0.0, scene_bounds=(-10, 10, -10, 10, -5, 10), time_step=0.004166666666666667), description='', tags=[])", "physics_score": {"objects_in_bounds": 1.0, "no_overlaps": 0.0, "stable_configuration": 1.0, "reasonable_masses": 1.0, "reasonable_scales": 1.0, "overall": 0.8}, "num_objects": 2}, {"text": "add a ramp", "predicted_scene": "PhysicsScene(scene_id='test_scene_1', objects=[PhysicsObject(object_id='ground', object_type=<ObjectType.PLANE: 'plane'>, position=Vector3(x=0, y=0, z=0), rotation=Vector3(x=0, y=0, z=0), scale=Vector3(x=10, y=10, z=1), mass=0, material=<MaterialType.STONE: 'stone'>, material_properties=MaterialProperties(friction=0.8, restitution=0.2, density=2700), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.5, 0.5, 0.5)), PhysicsObject(object_id='decoded_obj_0', object_type=<ObjectType.RAMP: 'ramp'>, position=Vector3(x=0.040486354380846024, y=-0.04064247012138367, z=-0.06663380563259125), rotation=Vector3(x=0.012995975092053413, y=0.37435632944107056, z=-0.006690341979265213), scale=Vector3(x=1.9838008880615234, y=0.2057657390832901, z=1.0960350036621094), mass=0.1, material=<MaterialType.WOOD: 'wood'>, material_properties=MaterialProperties(friction=0.7, restitution=0.5, density=600), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.7, 0.3, 0.3)), PhysicsObject(object_id='decoded_obj_1', object_type=<ObjectType.SPHERE: 'sphere'>, position=Vector3(x=-0.7286783456802368, y=-0.02252955734729767, z=1.5556217432022095), rotation=Vector3(x=-0.005647291429340839, y=0.013534808531403542, z=0.008416913449764252), scale=Vector3(x=0.09660179913043976, y=0.09824901819229126, z=0.09298456460237503), mass=1.5248963832855225, material=<MaterialType.RUBBER: 'rubber'>, material_properties=MaterialProperties(friction=0.8, restitution=0.9, density=1200), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.7, 0.3, 0.3))], environment=SceneEnvironment(gravity=Vector3(x=-0.0005269944667816162, y=0.0017336122691631317, z=-9.729324340820312), air_resistance=0.0, scene_bounds=(-10, 10, -10, 10, -5, 10), time_step=0.004166666666666667), description='', tags=[])", "physics_score": {"objects_in_bounds": 1.0, "no_overlaps": 1.0, "stable_configuration": 1.0, "reasonable_masses": 1.0, "reasonable_scales": 1.0, "overall": 1.0}, "num_objects": 2}, {"text": "place a sphere on a ramp", "predicted_scene": "PhysicsScene(scene_id='test_scene_2', objects=[PhysicsObject(object_id='ground', object_type=<ObjectType.PLANE: 'plane'>, position=Vector3(x=0, y=0, z=0), rotation=Vector3(x=0, y=0, z=0), scale=Vector3(x=10, y=10, z=1), mass=0, material=<MaterialType.STONE: 'stone'>, material_properties=MaterialProperties(friction=0.8, restitution=0.2, density=2700), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.5, 0.5, 0.5)), PhysicsObject(object_id='decoded_obj_0', object_type=<ObjectType.RAMP: 'ramp'>, position=Vector3(x=-0.010285211727023125, y=-0.08912132680416107, z=0.1518808752298355), rotation=Vector3(x=0.012061314657330513, y=0.38624095916748047, z=0.005122523754835129), scale=Vector3(x=1.9891256093978882, y=0.21143345534801483, z=1.0866388082504272), mass=0.1, material=<MaterialType.WOOD: 'wood'>, material_properties=MaterialProperties(friction=0.7, restitution=0.5, density=600), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.7, 0.3, 0.3)), PhysicsObject(object_id='decoded_obj_1', object_type=<ObjectType.SPHERE: 'sphere'>, position=Vector3(x=-0.866909384727478, y=-0.008749619126319885, z=1.8775148391723633), rotation=Vector3(x=0.005159820429980755, y=-0.015085553750395775, z=0.018747352063655853), scale=Vector3(x=0.10977586358785629, y=0.11382949352264404, z=0.11096558719873428), mass=1.8562417030334473, material=<MaterialType.WOOD: 'wood'>, material_properties=MaterialProperties(friction=0.7, restitution=0.5, density=600), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.7, 0.3, 0.3))], environment=SceneEnvironment(gravity=Vector3(x=-0.004006564617156982, y=-0.0039539821445941925, z=-9.608049392700195), air_resistance=0.0, scene_bounds=(-10, 10, -10, 10, -5, 10), time_step=0.004166666666666667), description='', tags=[])", "physics_score": {"objects_in_bounds": 1.0, "no_overlaps": 1.0, "stable_configuration": 1.0, "reasonable_masses": 1.0, "reasonable_scales": 1.0, "overall": 1.0}, "num_objects": 2}, {"text": "create two boxes", "predicted_scene": "PhysicsScene(scene_id='test_scene_3', objects=[PhysicsObject(object_id='ground', object_type=<ObjectType.PLANE: 'plane'>, position=Vector3(x=0, y=0, z=0), rotation=Vector3(x=0, y=0, z=0), scale=Vector3(x=10, y=10, z=1), mass=0, material=<MaterialType.STONE: 'stone'>, material_properties=MaterialProperties(friction=0.8, restitution=0.2, density=2700), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.5, 0.5, 0.5)), PhysicsObject(object_id='decoded_obj_0', object_type=<ObjectType.SPHERE: 'sphere'>, position=Vector3(x=-0.37076517939567566, y=-0.09402596950531006, z=1.6197768449783325), rotation=Vector3(x=-0.007482452318072319, y=0.08005858957767487, z=-0.008456069976091385), scale=Vector3(x=0.90605628490448, y=0.1874411255121231, z=0.4109923243522644), mass=1.4866044521331787, material=<MaterialType.WOOD: 'wood'>, material_properties=MaterialProperties(friction=0.7, restitution=0.5, density=600), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.7, 0.3, 0.3)), PhysicsObject(object_id='decoded_obj_1', object_type=<ObjectType.SPHERE: 'sphere'>, position=Vector3(x=-0.46015140414237976, y=0.005865350365638733, z=1.3905396461486816), rotation=Vector3(x=0.01201386284083128, y=0.003050321713089943, z=-0.0009626075625419617), scale=Vector3(x=0.0914204940199852, y=0.09378061443567276, z=0.09459526836872101), mass=1.3072453737258911, material=<MaterialType.RUBBER: 'rubber'>, material_properties=MaterialProperties(friction=0.8, restitution=0.9, density=1200), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.7, 0.3, 0.3))], environment=SceneEnvironment(gravity=Vector3(x=-0.0009756684303283691, y=-3.199651837348938e-05, z=-9.815896034240723), air_resistance=0.0, scene_bounds=(-10, 10, -10, 10, -5, 10), time_step=0.004166666666666667), description='', tags=[])", "physics_score": {"objects_in_bounds": 1.0, "no_overlaps": 0.0, "stable_configuration": 1.0, "reasonable_masses": 1.0, "reasonable_scales": 1.0, "overall": 0.8}, "num_objects": 2}, {"text": "add a bouncy ball and a wooden ramp", "predicted_scene": "PhysicsScene(scene_id='test_scene_4', objects=[PhysicsObject(object_id='ground', object_type=<ObjectType.PLANE: 'plane'>, position=Vector3(x=0, y=0, z=0), rotation=Vector3(x=0, y=0, z=0), scale=Vector3(x=10, y=10, z=1), mass=0, material=<MaterialType.STONE: 'stone'>, material_properties=MaterialProperties(friction=0.8, restitution=0.2, density=2700), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.5, 0.5, 0.5)), PhysicsObject(object_id='decoded_obj_0', object_type=<ObjectType.RAMP: 'ramp'>, position=Vector3(x=-0.04603235051035881, y=-0.09694181382656097, z=0.3919684886932373), rotation=Vector3(x=0.02082379348576069, y=0.36303165555000305, z=0.012936588376760483), scale=Vector3(x=1.9753552675247192, y=0.22173257172107697, z=1.0198942422866821), mass=0.18238888680934906, material=<MaterialType.WOOD: 'wood'>, material_properties=MaterialProperties(friction=0.7, restitution=0.5, density=600), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.7, 0.3, 0.3)), PhysicsObject(object_id='decoded_obj_1', object_type=<ObjectType.SPHERE: 'sphere'>, position=Vector3(x=-0.8515611290931702, y=-0.006627947092056274, z=1.9843745231628418), rotation=Vector3(x=0.011055122129619122, y=-0.033455200493335724, z=0.022328607738018036), scale=Vector3(x=0.13487130403518677, y=0.1379784345626831, z=0.13331839442253113), mass=2.0881876945495605, material=<MaterialType.WOOD: 'wood'>, material_properties=MaterialProperties(friction=0.7, restitution=0.5, density=600), initial_velocity=Vector3(x=0.0, y=0.0, z=0.0), initial_angular_velocity=Vector3(x=0.0, y=0.0, z=0.0), color=(0.7, 0.3, 0.3))], environment=SceneEnvironment(gravity=Vector3(x=0.0009428858757019043, y=0.005040835589170456, z=-9.756749153137207), air_resistance=0.0, scene_bounds=(-10, 10, -10, 10, -5, 10), time_step=0.004166666666666667), description='', tags=[])", "physics_score": {"objects_in_bounds": 1.0, "no_overlaps": 1.0, "stable_configuration": 1.0, "reasonable_masses": 1.0, "reasonable_scales": 1.0, "overall": 1.0}, "num_objects": 2}], "test_set_size": 200}