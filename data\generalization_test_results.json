{"total_tests": 2, "tests_passed": 2, "tests_failed": 0, "category_results": {"novel_objects": {"passed": 1, "total": 1}, "complex_relationships": {"passed": 1, "total": 1}}, "difficulty_analysis": {"3": {"passed": 1, "total": 1}, "4": {"passed": 1, "total": 1}}, "novel_aspects_analysis": {"pyramid": {"handled": 0, "total": 1}, "three-way relationship": {"handled": 0, "total": 1}}, "individual_results": [{"test_id": "demo_1", "category": "novel_objects", "input_text": "create a pyramid", "success": true, "ml_prediction_success": true, "relational_parsing_success": false, "physics_simulation_success": true, "validation_score": 1.0, "errors": [], "novel_aspects_handled": ["ml_prediction", "physics_simulation", "validation"], "execution_time": 1.2510478496551514}, {"test_id": "demo_2", "category": "complex_relationships", "input_text": "put the ball between the two boxes", "success": true, "ml_prediction_success": true, "relational_parsing_success": true, "physics_simulation_success": true, "validation_score": 1.0, "errors": [], "novel_aspects_handled": ["ml_prediction", "relational_parsing", "physics_simulation", "validation"], "execution_time": 1.1524388790130615}], "overall_score": 1.0, "execution_time": 2.403486728668213}