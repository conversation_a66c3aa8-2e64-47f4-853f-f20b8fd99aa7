<?xml version="1.0" ?>
<robot name="cube">
  <link name="baseLink">
     <contact>
      <lateral_friction value="1"/>
  </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value="1.0"/>
       <inertia ixx="100" ixy="0" ixz="0" iyy="100" iyz="0" izz="100"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
				<mesh filename="cube.obj" scale="1 1 1"/>
      </geometry>
       <material name="white">
        <color rgba="1 1 1 1"/>
      </material>
    </visual>
    <collision group="0" mask="0">
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
	 	<box size="1 1 1"/>
      </geometry>
    </collision>
  </link>
</robot>

