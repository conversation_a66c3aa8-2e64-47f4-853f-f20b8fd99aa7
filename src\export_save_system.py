"""
Export/Save System
Provides functionality to save scenes, export physics data, and load previous sessions
while maintaining compatibility with the existing interface.
"""

import json
import pickle
import os
import time
import zipfile
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import numpy as np

from dynamic_scene_representation import DynamicPhysicsScene, DynamicPhysicsObject
from conversational_context import ConversationalContext


@dataclass
class SessionData:
    """Complete session data for save/load."""
    session_id: str
    timestamp: str
    scene_data: Dict[str, Any]
    conversation_history: List[Dict[str, Any]]
    physics_results: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    
    def to_dict(self):
        return asdict(self)


@dataclass
class ExportSettings:
    """Settings for data export."""
    include_scene: bool = True
    include_conversation: bool = True
    include_physics_data: bool = True
    include_metadata: bool = True
    format: str = "json"  # json, pickle, csv
    compression: bool = False


class ExportSaveSystem:
    """Handles saving, loading, and exporting of physics sessions."""
    
    def __init__(self, save_directory: str = "saved_sessions"):
        """Initialize export/save system."""
        self.save_directory = save_directory
        self.ensure_save_directory()
        
        # Session tracking
        self.current_session_id = None
        self.auto_save_enabled = True
        self.auto_save_interval = 300  # 5 minutes
        self.last_auto_save = time.time()
        
        print(f"💾 Export/Save system initialized (directory: {save_directory})")
    
    def ensure_save_directory(self):
        """Ensure save directory exists."""
        if not os.path.exists(self.save_directory):
            os.makedirs(self.save_directory)
            print(f"📁 Created save directory: {self.save_directory}")
    
    def generate_session_id(self) -> str:
        """Generate unique session ID."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"session_{timestamp}"
    
    def save_session(self, scene: DynamicPhysicsScene, 
                    context: ConversationalContext = None,
                    physics_results: List[Dict[str, Any]] = None,
                    session_name: str = None) -> str:
        """Save complete session data."""
        # Generate session ID if not provided
        if not session_name:
            session_id = self.generate_session_id()
        else:
            session_id = f"session_{session_name}_{int(time.time())}"
        
        # Prepare session data
        session_data = SessionData(
            session_id=session_id,
            timestamp=datetime.now().isoformat(),
            scene_data=scene.to_dict() if scene else {},
            conversation_history=self._extract_conversation_history(context),
            physics_results=physics_results or [],
            metadata=self._generate_metadata(scene, context)
        )
        
        # Save to file
        save_path = os.path.join(self.save_directory, f"{session_id}.json")
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(session_data.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.current_session_id = session_id
            print(f"💾 Session saved: {save_path}")
            return session_id
            
        except Exception as e:
            print(f"❌ Failed to save session: {e}")
            return None
    
    def load_session(self, session_id: str) -> Optional[SessionData]:
        """Load session data."""
        save_path = os.path.join(self.save_directory, f"{session_id}.json")
        
        if not os.path.exists(save_path):
            print(f"❌ Session file not found: {save_path}")
            return None
        
        try:
            with open(save_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            session_data = SessionData(**data)
            print(f"📂 Session loaded: {session_id}")
            return session_data
            
        except Exception as e:
            print(f"❌ Failed to load session: {e}")
            return None
    
    def list_saved_sessions(self) -> List[Dict[str, Any]]:
        """List all saved sessions with metadata."""
        sessions = []
        
        for filename in os.listdir(self.save_directory):
            if filename.endswith('.json') and filename.startswith('session_'):
                session_id = filename[:-5]  # Remove .json extension
                
                try:
                    session_data = self.load_session(session_id)
                    if session_data:
                        sessions.append({
                            'session_id': session_id,
                            'timestamp': session_data.timestamp,
                            'object_count': session_data.metadata.get('object_count', 0),
                            'conversation_turns': len(session_data.conversation_history),
                            'file_size': os.path.getsize(os.path.join(self.save_directory, filename))
                        })
                except Exception as e:
                    print(f"⚠️ Error reading session {session_id}: {e}")
        
        # Sort by timestamp (newest first)
        sessions.sort(key=lambda x: x['timestamp'], reverse=True)
        return sessions
    
    def delete_session(self, session_id: str) -> bool:
        """Delete a saved session."""
        save_path = os.path.join(self.save_directory, f"{session_id}.json")
        
        if os.path.exists(save_path):
            try:
                os.remove(save_path)
                print(f"🗑️ Session deleted: {session_id}")
                return True
            except Exception as e:
                print(f"❌ Failed to delete session: {e}")
                return False
        else:
            print(f"❌ Session not found: {session_id}")
            return False
    
    def export_scene_data(self, scene: DynamicPhysicsScene, 
                         export_path: str, settings: ExportSettings = None) -> bool:
        """Export scene data in various formats."""
        if not settings:
            settings = ExportSettings()
        
        try:
            if settings.format == "json":
                return self._export_json(scene, export_path, settings)
            elif settings.format == "csv":
                return self._export_csv(scene, export_path, settings)
            elif settings.format == "pickle":
                return self._export_pickle(scene, export_path, settings)
            else:
                print(f"❌ Unsupported export format: {settings.format}")
                return False
                
        except Exception as e:
            print(f"❌ Export failed: {e}")
            return False
    
    def _export_json(self, scene: DynamicPhysicsScene, export_path: str, settings: ExportSettings) -> bool:
        """Export scene data as JSON."""
        export_data = {
            'export_timestamp': datetime.now().isoformat(),
            'export_settings': asdict(settings)
        }
        
        if settings.include_scene:
            export_data['scene'] = scene.to_dict()
        
        if settings.include_metadata:
            export_data['metadata'] = self._generate_metadata(scene)
        
        # Write to file
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        print(f"📤 Scene exported to JSON: {export_path}")
        return True
    
    def _export_csv(self, scene: DynamicPhysicsScene, export_path: str, settings: ExportSettings) -> bool:
        """Export scene data as CSV."""
        import csv
        
        with open(export_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Header
            writer.writerow([
                'object_id', 'object_type', 'material', 
                'pos_x', 'pos_y', 'pos_z',
                'rot_x', 'rot_y', 'rot_z',
                'scale_x', 'scale_y', 'scale_z',
                'mass'
            ])
            
            # Object data
            for obj_id, obj in scene.objects.items():
                writer.writerow([
                    obj_id, obj.object_type.value, obj.material.value,
                    obj.position.x, obj.position.y, obj.position.z,
                    obj.rotation.x, obj.rotation.y, obj.rotation.z,
                    obj.scale.x, obj.scale.y, obj.scale.z,
                    obj.mass
                ])
        
        print(f"📤 Scene exported to CSV: {export_path}")
        return True
    
    def _export_pickle(self, scene: DynamicPhysicsScene, export_path: str, settings: ExportSettings) -> bool:
        """Export scene data as pickle."""
        export_data = {
            'scene': scene,
            'export_timestamp': datetime.now().isoformat(),
            'settings': settings
        }
        
        with open(export_path, 'wb') as f:
            pickle.dump(export_data, f)
        
        print(f"📤 Scene exported to pickle: {export_path}")
        return True
    
    def import_scene_data(self, import_path: str) -> Optional[DynamicPhysicsScene]:
        """Import scene data from file."""
        if not os.path.exists(import_path):
            print(f"❌ Import file not found: {import_path}")
            return None
        
        try:
            file_ext = os.path.splitext(import_path)[1].lower()
            
            if file_ext == '.json':
                return self._import_json(import_path)
            elif file_ext == '.pkl' or file_ext == '.pickle':
                return self._import_pickle(import_path)
            else:
                print(f"❌ Unsupported import format: {file_ext}")
                return None
                
        except Exception as e:
            print(f"❌ Import failed: {e}")
            return None
    
    def _import_json(self, import_path: str) -> Optional[DynamicPhysicsScene]:
        """Import scene from JSON file."""
        with open(import_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'scene' in data:
            scene = DynamicPhysicsScene.from_dict(data['scene'])
            print(f"📥 Scene imported from JSON: {import_path}")
            return scene
        else:
            print(f"❌ No scene data found in JSON file")
            return None
    
    def _import_pickle(self, import_path: str) -> Optional[DynamicPhysicsScene]:
        """Import scene from pickle file."""
        with open(import_path, 'rb') as f:
            data = pickle.load(f)
        
        if isinstance(data, dict) and 'scene' in data:
            scene = data['scene']
            print(f"📥 Scene imported from pickle: {import_path}")
            return scene
        elif isinstance(data, DynamicPhysicsScene):
            print(f"📥 Scene imported from pickle: {import_path}")
            return data
        else:
            print(f"❌ Invalid pickle data format")
            return None
    
    def auto_save_check(self, scene: DynamicPhysicsScene, context: ConversationalContext = None):
        """Check if auto-save should be triggered."""
        if not self.auto_save_enabled:
            return
        
        current_time = time.time()
        if current_time - self.last_auto_save > self.auto_save_interval:
            auto_save_id = self.save_session(scene, context, session_name="auto")
            if auto_save_id:
                self.last_auto_save = current_time
                print(f"💾 Auto-save completed: {auto_save_id}")
    
    def _extract_conversation_history(self, context: ConversationalContext) -> List[Dict[str, Any]]:
        """Extract conversation history from context."""
        if not context:
            return []
        
        history = []
        for turn in context.conversation_history:
            history.append({
                'turn_id': turn.turn_id,
                'timestamp': turn.timestamp,
                'user_input': turn.user_input,
                'system_response': turn.system_response,
                'user_intent': turn.user_intent,
                'entities_mentioned': turn.entities_mentioned
            })
        
        return history
    
    def _generate_metadata(self, scene: DynamicPhysicsScene = None, 
                          context: ConversationalContext = None) -> Dict[str, Any]:
        """Generate metadata for session."""
        metadata = {
            'created_timestamp': datetime.now().isoformat(),
            'system_version': '1.0.0',
            'object_count': scene.get_object_count() if scene else 0,
            'conversation_turns': len(context.conversation_history) if context else 0
        }
        
        if scene:
            # Object type distribution
            object_types = {}
            material_types = {}
            
            for obj in scene.objects.values():
                obj_type = obj.object_type.value
                material = obj.material.value
                
                object_types[obj_type] = object_types.get(obj_type, 0) + 1
                material_types[material] = material_types.get(material, 0) + 1
            
            metadata['object_types'] = object_types
            metadata['material_types'] = material_types
        
        return metadata
    
    def create_backup(self, backup_name: str = None) -> bool:
        """Create backup of all saved sessions."""
        if not backup_name:
            backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_path = f"{backup_name}.zip"
        
        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for filename in os.listdir(self.save_directory):
                    if filename.endswith('.json'):
                        file_path = os.path.join(self.save_directory, filename)
                        zipf.write(file_path, filename)
            
            print(f"💾 Backup created: {backup_path}")
            return True
            
        except Exception as e:
            print(f"❌ Backup failed: {e}")
            return False
    
    def get_storage_info(self) -> Dict[str, Any]:
        """Get storage usage information."""
        total_size = 0
        file_count = 0
        
        for filename in os.listdir(self.save_directory):
            if filename.endswith('.json'):
                file_path = os.path.join(self.save_directory, filename)
                total_size += os.path.getsize(file_path)
                file_count += 1
        
        return {
            'total_sessions': file_count,
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'average_size_kb': round(total_size / file_count / 1024, 2) if file_count > 0 else 0,
            'save_directory': self.save_directory
        }


def test_export_save_system():
    """Test the export/save system."""
    print("Testing Export/Save System...")
    
    # Create test system
    save_system = ExportSaveSystem("test_saves")
    
    # Create test scene
    test_scene = DynamicPhysicsScene("test_scene")
    
    # Test saving
    session_id = save_system.save_session(test_scene, session_name="test")
    print(f"✅ Session saved: {session_id}")
    
    # Test loading
    loaded_session = save_system.load_session(session_id)
    print(f"✅ Session loaded: {loaded_session is not None}")
    
    # Test listing
    sessions = save_system.list_saved_sessions()
    print(f"✅ Found {len(sessions)} saved sessions")
    
    # Test export
    export_path = "test_export.json"
    success = save_system.export_scene_data(test_scene, export_path)
    print(f"✅ Export successful: {success}")
    
    # Test import
    imported_scene = save_system.import_scene_data(export_path)
    print(f"✅ Import successful: {imported_scene is not None}")
    
    # Test storage info
    storage_info = save_system.get_storage_info()
    print(f"✅ Storage info: {storage_info['total_sessions']} sessions, {storage_info['total_size_mb']} MB")
    
    # Cleanup
    if session_id:
        save_system.delete_session(session_id)
    if os.path.exists(export_path):
        os.remove(export_path)
    if os.path.exists("test_saves"):
        import shutil
        shutil.rmtree("test_saves")
    
    print("✅ Export/Save system test completed!")


if __name__ == "__main__":
    test_export_save_system()
