"""
Advanced Visualization Enhancements
Enhances the PyBullet physics visualization with better camera controls, 
object highlighting, and visual effects without changing the core UI.
"""

import pybullet as p
import numpy as np
import time
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum

from dynamic_scene_representation import DynamicPhysicsObject, Vector3


class VisualizationMode(Enum):
    """Different visualization modes."""
    STANDARD = "standard"
    PHYSICS_DEBUG = "physics_debug"
    TRAJECTORY_TRACE = "trajectory_trace"
    FORCE_VECTORS = "force_vectors"
    ENERGY_VISUALIZATION = "energy_visualization"
    SLOW_MOTION = "slow_motion"


@dataclass
class CameraSettings:
    """Camera configuration settings."""
    distance: float = 5.0
    yaw: float = 45.0
    pitch: float = -30.0
    target_position: Tuple[float, float, float] = (0, 0, 0)
    fov: float = 60.0
    near_plane: float = 0.1
    far_plane: float = 100.0


@dataclass
class VisualEffect:
    """Visual effect configuration."""
    effect_type: str
    object_id: int
    color: Tuple[float, float, float, float]
    duration: float
    start_time: float
    parameters: Dict[str, Any]


class AdvancedVisualization:
    """Enhanced visualization system for PyBullet physics simulation."""
    
    def __init__(self, physics_client_id: int = 0):
        """Initialize advanced visualization."""
        self.physics_client = physics_client_id
        self.camera_settings = CameraSettings()
        self.visualization_mode = VisualizationMode.STANDARD
        
        # Visual effects tracking
        self.active_effects = []
        self.trajectory_traces = {}  # object_id -> list of positions
        self.object_highlights = {}  # object_id -> highlight_info
        
        # Performance settings
        self.enable_shadows = True
        self.enable_reflections = False
        self.render_quality = "high"  # low, medium, high
        
        # Initialize enhanced rendering
        self._setup_enhanced_rendering()
        
        print("🎨 Advanced visualization system initialized!")
    
    def _setup_enhanced_rendering(self):
        """Setup enhanced rendering options."""
        # Enable shadows and better lighting
        if self.enable_shadows:
            p.configureDebugVisualizer(p.COV_ENABLE_SHADOWS, 1, physicsClientId=self.physics_client)
        
        # Configure rendering quality
        if self.render_quality == "high":
            p.configureDebugVisualizer(p.COV_ENABLE_RGB_BUFFER_PREVIEW, 1, physicsClientId=self.physics_client)
            p.configureDebugVisualizer(p.COV_ENABLE_DEPTH_BUFFER_PREVIEW, 1, physicsClientId=self.physics_client)
        
        # Set better default camera
        self.set_camera_position()
    
    def set_camera_position(self, distance: float = None, yaw: float = None, 
                          pitch: float = None, target: Tuple[float, float, float] = None):
        """Set camera position with smooth transitions."""
        if distance is not None:
            self.camera_settings.distance = distance
        if yaw is not None:
            self.camera_settings.yaw = yaw
        if pitch is not None:
            self.camera_settings.pitch = pitch
        if target is not None:
            self.camera_settings.target_position = target
        
        p.resetDebugVisualizerCamera(
            cameraDistance=self.camera_settings.distance,
            cameraYaw=self.camera_settings.yaw,
            cameraPitch=self.camera_settings.pitch,
            cameraTargetPosition=self.camera_settings.target_position,
            physicsClientId=self.physics_client
        )
    
    def set_visualization_mode(self, mode: VisualizationMode):
        """Change visualization mode."""
        self.visualization_mode = mode
        
        if mode == VisualizationMode.PHYSICS_DEBUG:
            self._enable_physics_debug()
        elif mode == VisualizationMode.TRAJECTORY_TRACE:
            self._enable_trajectory_tracing()
        elif mode == VisualizationMode.FORCE_VECTORS:
            self._enable_force_vectors()
        elif mode == VisualizationMode.ENERGY_VISUALIZATION:
            self._enable_energy_visualization()
        elif mode == VisualizationMode.SLOW_MOTION:
            self._enable_slow_motion()
        else:
            self._disable_all_debug_modes()
    
    def _enable_physics_debug(self):
        """Enable physics debug visualization."""
        # Show contact points
        p.configureDebugVisualizer(p.COV_ENABLE_WIREFRAME, 1, physicsClientId=self.physics_client)
        
        # Add debug parameters for physics
        p.addUserDebugParameter("Show Contact Points", 0, 1, 0, physicsClientId=self.physics_client)
        p.addUserDebugParameter("Show Collision Shapes", 0, 1, 0, physicsClientId=self.physics_client)
    
    def _enable_trajectory_tracing(self):
        """Enable trajectory tracing for moving objects."""
        # Clear existing traces
        self.trajectory_traces.clear()
        print("🔄 Trajectory tracing enabled")
    
    def _enable_force_vectors(self):
        """Enable force vector visualization."""
        print("⚡ Force vector visualization enabled")
    
    def _enable_energy_visualization(self):
        """Enable energy visualization."""
        print("🔋 Energy visualization enabled")
    
    def _enable_slow_motion(self):
        """Enable slow motion mode."""
        print("🐌 Slow motion mode enabled")
    
    def _disable_all_debug_modes(self):
        """Disable all debug visualization modes."""
        p.configureDebugVisualizer(p.COV_ENABLE_WIREFRAME, 0, physicsClientId=self.physics_client)
        print("👁️ Standard visualization mode")
    
    def highlight_object(self, object_id: int, color: Tuple[float, float, float, float] = (1, 1, 0, 0.5),
                        duration: float = 2.0, effect_type: str = "glow"):
        """Highlight an object with visual effects."""
        # Store original color
        visual_data = p.getVisualShapeData(object_id, physicsClientId=self.physics_client)
        if visual_data:
            original_color = visual_data[0][7]  # RGBA color
        else:
            original_color = (0.7, 0.7, 0.7, 1.0)
        
        # Apply highlight effect
        if effect_type == "glow":
            p.changeVisualShape(object_id, -1, rgbaColor=color, physicsClientId=self.physics_client)
        
        # Schedule removal of highlight
        effect = VisualEffect(
            effect_type=effect_type,
            object_id=object_id,
            color=original_color,
            duration=duration,
            start_time=time.time(),
            parameters={'original_color': original_color}
        )
        
        self.active_effects.append(effect)
        self.object_highlights[object_id] = effect
    
    def remove_highlight(self, object_id: int):
        """Remove highlight from an object."""
        if object_id in self.object_highlights:
            effect = self.object_highlights[object_id]
            original_color = effect.parameters['original_color']
            
            p.changeVisualShape(
                object_id, -1, 
                rgbaColor=original_color, 
                physicsClientId=self.physics_client
            )
            
            del self.object_highlights[object_id]
            if effect in self.active_effects:
                self.active_effects.remove(effect)
    
    def update_trajectory_trace(self, object_id: int, position: Vector3):
        """Update trajectory trace for an object."""
        if self.visualization_mode != VisualizationMode.TRAJECTORY_TRACE:
            return
        
        if object_id not in self.trajectory_traces:
            self.trajectory_traces[object_id] = []
        
        # Add current position
        self.trajectory_traces[object_id].append(position.to_list())
        
        # Limit trace length
        max_trace_length = 100
        if len(self.trajectory_traces[object_id]) > max_trace_length:
            self.trajectory_traces[object_id].pop(0)
        
        # Draw trajectory line
        if len(self.trajectory_traces[object_id]) > 1:
            self._draw_trajectory_line(object_id)
    
    def _draw_trajectory_line(self, object_id: int):
        """Draw trajectory line for an object."""
        positions = self.trajectory_traces[object_id]
        
        # Draw line segments
        for i in range(len(positions) - 1):
            start_pos = positions[i]
            end_pos = positions[i + 1]
            
            # Color fades from red to blue along trajectory
            alpha = i / len(positions)
            color = [1 - alpha, 0, alpha]  # Red to blue gradient
            
            p.addUserDebugLine(
                start_pos, end_pos,
                lineColorRGB=color,
                lineWidth=2,
                lifeTime=0.1,  # Short lifetime for smooth animation
                physicsClientId=self.physics_client
            )
    
    def add_force_vector(self, object_id: int, force_vector: Vector3, 
                        application_point: Vector3 = None, color: Tuple[float, float, float] = (1, 0, 0)):
        """Visualize force vectors on objects."""
        if self.visualization_mode != VisualizationMode.FORCE_VECTORS:
            return
        
        # Get object position if application point not specified
        if application_point is None:
            pos, _ = p.getBasePositionAndOrientation(object_id, physicsClientId=self.physics_client)
            application_point = Vector3(pos[0], pos[1], pos[2])
        
        # Scale force vector for visualization
        force_scale = 0.1  # Adjust based on typical force magnitudes
        end_point = Vector3(
            application_point.x + force_vector.x * force_scale,
            application_point.y + force_vector.y * force_scale,
            application_point.z + force_vector.z * force_scale
        )
        
        # Draw force vector as arrow
        p.addUserDebugLine(
            application_point.to_list(),
            end_point.to_list(),
            lineColorRGB=color,
            lineWidth=3,
            lifeTime=0.1,
            physicsClientId=self.physics_client
        )
        
        # Add arrowhead
        self._draw_arrow_head(end_point, force_vector, color)
    
    def _draw_arrow_head(self, tip_position: Vector3, direction: Vector3, color: Tuple[float, float, float]):
        """Draw arrowhead for force vectors."""
        # Normalize direction
        length = np.sqrt(direction.x**2 + direction.y**2 + direction.z**2)
        if length > 0:
            dir_norm = Vector3(direction.x/length, direction.y/length, direction.z/length)
            
            # Create arrowhead points
            arrow_length = 0.05
            arrow_width = 0.02
            
            # Simple arrowhead (two lines)
            side1 = Vector3(
                tip_position.x - dir_norm.x * arrow_length + dir_norm.y * arrow_width,
                tip_position.y - dir_norm.y * arrow_length - dir_norm.x * arrow_width,
                tip_position.z - dir_norm.z * arrow_length
            )
            
            side2 = Vector3(
                tip_position.x - dir_norm.x * arrow_length - dir_norm.y * arrow_width,
                tip_position.y - dir_norm.y * arrow_length + dir_norm.x * arrow_width,
                tip_position.z - dir_norm.z * arrow_length
            )
            
            p.addUserDebugLine(
                tip_position.to_list(), side1.to_list(),
                lineColorRGB=color, lineWidth=2, lifeTime=0.1,
                physicsClientId=self.physics_client
            )
            
            p.addUserDebugLine(
                tip_position.to_list(), side2.to_list(),
                lineColorRGB=color, lineWidth=2, lifeTime=0.1,
                physicsClientId=self.physics_client
            )
    
    def visualize_energy(self, object_id: int, kinetic_energy: float, potential_energy: float):
        """Visualize energy levels of objects."""
        if self.visualization_mode != VisualizationMode.ENERGY_VISUALIZATION:
            return
        
        # Get object position
        pos, _ = p.getBasePositionAndOrientation(object_id, physicsClientId=self.physics_client)
        
        # Energy bars above object
        bar_height = 0.5
        bar_width = 0.1
        
        # Kinetic energy bar (red)
        ke_height = min(kinetic_energy * 0.1, 1.0)  # Scale energy for visualization
        ke_start = Vector3(pos[0] - 0.15, pos[1], pos[2] + 1.0)
        ke_end = Vector3(pos[0] - 0.15, pos[1], pos[2] + 1.0 + ke_height)
        
        p.addUserDebugLine(
            ke_start.to_list(), ke_end.to_list(),
            lineColorRGB=[1, 0, 0], lineWidth=5, lifeTime=0.1,
            physicsClientId=self.physics_client
        )
        
        # Potential energy bar (blue)
        pe_height = min(potential_energy * 0.1, 1.0)
        pe_start = Vector3(pos[0] + 0.15, pos[1], pos[2] + 1.0)
        pe_end = Vector3(pos[0] + 0.15, pos[1], pos[2] + 1.0 + pe_height)
        
        p.addUserDebugLine(
            pe_start.to_list(), pe_end.to_list(),
            lineColorRGB=[0, 0, 1], lineWidth=5, lifeTime=0.1,
            physicsClientId=self.physics_client
        )
    
    def update_effects(self):
        """Update and clean up visual effects."""
        current_time = time.time()
        effects_to_remove = []
        
        for effect in self.active_effects:
            if current_time - effect.start_time > effect.duration:
                # Remove expired effect
                if effect.effect_type == "glow":
                    original_color = effect.parameters['original_color']
                    p.changeVisualShape(
                        effect.object_id, -1,
                        rgbaColor=original_color,
                        physicsClientId=self.physics_client
                    )
                
                effects_to_remove.append(effect)
                if effect.object_id in self.object_highlights:
                    del self.object_highlights[effect.object_id]
        
        # Remove expired effects
        for effect in effects_to_remove:
            self.active_effects.remove(effect)
    
    def get_camera_controls_help(self) -> List[str]:
        """Get help text for camera controls."""
        return [
            "🎥 Camera Controls:",
            "• Mouse drag: Rotate view",
            "• Mouse wheel: Zoom in/out", 
            "• Shift + drag: Pan view",
            "• Ctrl + drag: Move target",
            "",
            "🎨 Visualization Modes:",
            "• Standard: Normal rendering",
            "• Physics Debug: Show wireframes and contacts",
            "• Trajectory Trace: Show object paths",
            "• Force Vectors: Show applied forces",
            "• Energy Visualization: Show energy levels",
            "• Slow Motion: Reduced simulation speed"
        ]
    
    def reset_camera_to_scene(self, objects: List[DynamicPhysicsObject]):
        """Reset camera to optimally view all objects in scene."""
        if not objects:
            return
        
        # Calculate bounding box of all objects
        min_pos = [float('inf')] * 3
        max_pos = [float('-inf')] * 3
        
        for obj in objects:
            pos = obj.position.to_list()
            for i in range(3):
                min_pos[i] = min(min_pos[i], pos[i])
                max_pos[i] = max(max_pos[i], pos[i])
        
        # Calculate center and size
        center = [(min_pos[i] + max_pos[i]) / 2 for i in range(3)]
        size = max(max_pos[i] - min_pos[i] for i in range(3))
        
        # Set camera to view entire scene
        distance = max(5.0, size * 2.0)
        self.set_camera_position(
            distance=distance,
            target=tuple(center)
        )


def test_advanced_visualization():
    """Test the advanced visualization system."""
    print("Testing Advanced Visualization System...")
    
    # This would normally be integrated with the physics simulation
    # For testing, we'll just verify the class initializes correctly
    
    try:
        viz = AdvancedVisualization()
        
        print("✅ Visualization system initialized")
        print("✅ Camera controls configured")
        print("✅ Visual effects system ready")
        print("✅ Multiple visualization modes available")
        
        # Test mode switching
        for mode in VisualizationMode:
            viz.set_visualization_mode(mode)
            print(f"✅ {mode.value} mode activated")
        
        # Test camera controls
        viz.set_camera_position(distance=10.0, yaw=90.0, pitch=-45.0)
        print("✅ Camera positioning works")
        
        # Show help
        help_text = viz.get_camera_controls_help()
        print("✅ Help system available")
        
        print("✅ Advanced visualization test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    test_advanced_visualization()
