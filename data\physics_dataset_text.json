{"metadata": {"num_examples": 100, "generated_at": 1755484894.82002, "format": "text_only"}, "examples": [{"id": "example_8baf670b", "text": "make a incline and set a 0.6kg rubber round object at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_15a402ee", "text": "place a 2.6kg round object in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_55b9fbdb", "text": "insert a rubber orb that will rebound off a bouncy box", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_3af1e57a", "text": "construct a scenario where a 1.4kg round object rolls down a wooden ramp and collides with a box", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_251ee61a", "text": "introduce a iron orb", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_5147a2a1", "text": "position a 3.9kg round object in the scene", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_b798895d", "text": "introduce a steel ball and a wooden slope", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_47b140a3", "text": "put a 1.0kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_f1d8fef9", "text": "drop a 2.8kg orb on top of a incline", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_6de19173", "text": "insert a marble block", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_76b14ed4", "text": "insert a polymer sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_70a2ff2d", "text": "Build a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_377e5955", "text": "place a 2.4kg sphere in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_f89bd629", "text": "build a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_44560968", "text": "drop a 3.1kg sphere in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_22f119e9", "text": "add a wooden ball that will rebound off a super elastic box", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_413d4dba", "text": "form a sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_0ac65e2a", "text": "make a orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_89ddd645", "text": "include a stone round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_f0107214", "text": "drop a 2.1kg round object in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_baf53877", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_cbf051a8", "text": "set a 1.1kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_83553d93", "text": "create a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_467862e1", "text": "include a bouncy ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_89b85da7", "text": "build a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_376ea606", "text": "include a plastic cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_dbc156c7", "text": "create a block and a round object next to each other", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_16750427", "text": "make a ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_a87806b5", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_536f331c", "text": "Set up a ramp with a sphere that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_ac024407", "text": "insert a highly elastic sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_bf555028", "text": "put a 1.1kg round object in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_06a8701c", "text": "place a 0.7kg round object in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_3e3337a7", "text": "position a 0.8kg ball on top of a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_8275f663", "text": "build a ramp and put a sphere on it", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_505aa297", "text": "Set up a inclined plane with a orb that will roll down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_ff343ae9", "text": "Set up a ramp with a sphere that will move down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_fa55e82a", "text": "position a 3.6kg orb in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_663a55b2", "text": "set a 1.1kg sphere in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_5047f53b", "text": "include a elastic ball", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_c204000a", "text": "construct a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_45c1f885", "text": "insert a wood sphere and a timber inclined plane", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_a3a9be48", "text": "insert a elastic round object", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_ff206c32", "text": "include a rubber ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_95b37f65", "text": "make a sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_39b00995", "text": "put a 0.4kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_eba445cb", "text": "include a super elastic ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_70b94dce", "text": "create a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_f62b1f42", "text": "make a incline and position a 2.2kg steel sphere at the top so it will roll down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_151ec00b", "text": "insert a wood round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_9a4f2d26", "text": "add a bouncy sphere", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_b8c4f717", "text": "build a sphere", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_77815101", "text": "insert a bouncy round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_386f3fda", "text": "drop a 2.0kg ball in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_b6455cf1", "text": "Create an empty physics scene", "tags": ["drop", "gravity", "falling"], "num_objects": 0}, {"id": "example_f1a2c53f", "text": "construct a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_e28a10c3", "text": "form a incline and set a 1.9kg timber round object at the top so it will slide down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_62a0695f", "text": "drop a 1.9kg ball in the scene", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_281475cd", "text": "position a 0.7kg round object in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_923dfe42", "text": "introduce a wooden round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_c145bb4b", "text": "create a orb", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_1d6195b5", "text": "set a 1.6kg orb in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_216de4db", "text": "insert a plastic box", "tags": ["drop", "gravity", "falling"], "num_objects": 3}, {"id": "example_84a67f01", "text": "construct a inclined plane and set a 1.5kg polymer sphere at the top so it will roll down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_49625df8", "text": "build a round object", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_0490832c", "text": "construct a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_3ae1a539", "text": "build a sphere", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_7b096a99", "text": "put a 1.0kg sphere in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_d6af387f", "text": "Set up a incline with a orb that will slide down", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_ed1a02eb", "text": "set a 0.6kg sphere on top of a slope", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_9d78684b", "text": "place a 1.1kg orb in the scene", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_a698e8d1", "text": "include a wood sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_3b0a0dc0", "text": "add a bouncy round object", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_9eb6c81c", "text": "place a 2.1kg ball in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_cc0c8e69", "text": "form a sphere", "tags": ["complex", "multi-object", "interaction"], "num_objects": 7}, {"id": "example_b6393ba3", "text": "put a 1.5kg round object in the scene", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_a8098121", "text": "introduce a metal block", "tags": ["drop", "gravity", "falling"], "num_objects": 2}, {"id": "example_d4da37ea", "text": "Build a inclined plane", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_fda1c1dd", "text": "build a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_56f5c702", "text": "drop a 2.0kg orb in the scene", "tags": ["complex", "multi-object", "interaction"], "num_objects": 6}, {"id": "example_f2026f46", "text": "Build a ramp", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 1}, {"id": "example_5351b5ed", "text": "make a round object", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_ff3e3b29", "text": "introduce a bouncy ball", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_cccc282f", "text": "make a ball", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_e9003ffa", "text": "add a rubber round object", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_826d3327", "text": "make a ramp and drop a 2.2kg marble sphere at the top so it will move down due to gravity", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 2}, {"id": "example_9bec0395", "text": "introduce a steel ball that will bounce off a metallic box", "tags": ["complex", "multi-object", "interaction"], "num_objects": 5}, {"id": "example_5e53fa78", "text": "insert a rubber orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 3}, {"id": "example_c03c74c1", "text": "make a orb", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_044e2f21", "text": "set a 1.0kg orb in the scene", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 2}, {"id": "example_3d210b0a", "text": "build a rectangular object", "tags": ["complex", "multi-object", "interaction"], "num_objects": 4}, {"id": "example_b2e1fe59", "text": "insert a polymer round object", "tags": ["collision", "impact", "momentum"], "num_objects": 1}, {"id": "example_35c43312", "text": "add a bouncy cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_7aeb0794", "text": "include a super elastic round object", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_40a1af9f", "text": "include a rubber orb", "tags": ["bouncing", "elastic", "restitution"], "num_objects": 1}, {"id": "example_ee1d8599", "text": "construct a ball", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_dfd96b1c", "text": "create a sphere", "tags": ["collision", "impact", "momentum"], "num_objects": 2}, {"id": "example_0c7d9cf5", "text": "introduce a rocky ball", "tags": ["ramp", "rolling", "incline", "gravity"], "num_objects": 3}, {"id": "example_c87f472a", "text": "construct a sphere", "tags": ["drop", "gravity", "falling"], "num_objects": 1}, {"id": "example_db0d65ad", "text": "form a cube", "tags": ["drop", "gravity", "falling"], "num_objects": 1}]}