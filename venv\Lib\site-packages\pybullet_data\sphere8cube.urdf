<?xml version="0.0" ?>
<robot name="urdf_robot">
  <link name="baseLink">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value="10.0"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
<mesh filename="cube.obj" scale="1 1 1"/>      
      </geometry>
      <material name="white">
        <color rgba="1 1 1 1"/>
        <specular rgb="11 1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0.4 0.4 0.4"/>
      <geometry>
	 		<sphere radius="0.1"/>
      </geometry>
    </collision>
    <collision>
      <origin rpy="0 0 0" xyz="-0.4 0.4 0.4"/>
      <geometry>
	 		<sphere radius="0.1"/>
      </geometry>
    </collision><collision>
      <origin rpy="0 0 0" xyz="0.4 -0.4 0.4"/>
      <geometry>
	 		<sphere radius="0.1"/>
      </geometry>
    </collision><collision>
      <origin rpy="0 0 0" xyz="-0.4 -0.4 0.4"/>
      <geometry>
	 		<sphere radius="0.1"/>
      </geometry>
    </collision><collision>
      <origin rpy="0 0 0" xyz="0.4 0.4 -0.4"/>
      <geometry>
	 		<sphere radius="0.1"/>
      </geometry>
    </collision><collision>
      <origin rpy="0 0 0" xyz="-0.4 0.4 -0.4"/>
      <geometry>
	 		<sphere radius="0.1"/>
      </geometry>
    </collision><collision>
      <origin rpy="0 0 0" xyz="0.4 -0.4 -0.4"/>
      <geometry>
	 		<sphere radius="0.1"/>
      </geometry>
    </collision><collision>
      <origin rpy="0 0 0" xyz="-0.4 -0.4 -0.4"/>
      <geometry>
	 		<sphere radius="0.1"/>
      </geometry>
    </collision>
  </link>
</robot>

