<?xml version="1.0" ?>
<robot name="cube">
  <link name="world"/>
  
  <link name="baseLink">
    <contact>
      <lateral_friction value="1.0"/>
      <rolling_friction value="0.0"/>
      <contact_cfm value="0.0"/>
      <contact_erp value="1.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value="1.0"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
<mesh filename="cube.obj" scale="1 1 1"/>
      </geometry>
       <material name="white">
        <color rgba="1 1 1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
<box size="1 1 1"/>
      </geometry>
    </collision>
  </link>
  <joint name="joint_baseLink_world" type="continuous">
    <parent link="world"/>
    <child link="baseLink"/>
<dynamics damping="1.0" friction="0.0001"/>
    <origin xyz="0 0 0"/>
<axis xyz="0 0 1"/>
  </joint>
</robot>

