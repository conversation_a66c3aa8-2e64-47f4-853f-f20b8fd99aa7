example_id,text_description,num_objects,scenario_type,complexity,tags,has_ramp,has_sphere,has_box
example_81ff524c,drop a 3.6kg sphere in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_a51b8206,put a 1.9kg orb in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_4bfea493,Build a slope,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_04b0d68b,Set up a inclined plane with a ball that will roll down,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_26aae0fc,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_c706996e,create a sphere,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_d6b318de,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_d2eabfe8,create a ramp and set a orb on it,5,complex,complex,"complex,multi-object,interaction",True,True,False
example_4550f717,create a slope and put a sphere on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_de0327d4,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_37f941e0,create a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_e4d03525,construct a ramp and put a 2.2kg icy sphere at the top so it will move down due to gravity,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_75b8afe3,set a 1.1kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_e06ab7a4,Build a ramp,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_1910176d,form a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_e4ada39f,put a 1.3kg sphere in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_0c0f504f,construct a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_78585049,set a 0.6kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_0d30101f,Build a inclined plane,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_51a03e6b,include a super elastic round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_c2cdb987,create a inclined plane and put a 0.6kg elastic sphere at the top so it will slide down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_fd887948,introduce a rubber round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_cce6c7d0,introduce a bouncy orb,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_21b433a0,put a 1.4kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_4150314f,create a orb,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_1f0e8652,place a 1.5kg orb in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_93d66be4,insert a polymer rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_b1e8dee4,put a 3.7kg orb in the scene,2,drop,simple,"drop,gravity,falling",False,True,False
example_01111ce5,introduce a timber ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_6782bda6,drop a 3.2kg ball in the scene,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_95c04516,drop a 2.4kg orb in the scene,3,drop,complex,"drop,gravity,falling",False,True,True
example_7c957174,place a 0.6kg ball in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_c6985d21,form a ramp and set a ball on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_2345cd4c,include a bouncy orb,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_c22769bd,insert a rubber orb that will rebound off a plastic box,2,drop,simple,"drop,gravity,falling",False,True,True
example_3a9d8481,introduce a rubber ball,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_79d5a44d,form a ramp and put a ball on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_38b97bdf,place a 1.7kg round object on top of a ramp,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_855e776f,Build a inclined plane,5,complex,complex,"complex,multi-object,interaction",True,False,True
example_f5e76c74,build a orb,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_b2369813,build a incline and position a 0.5kg polymer ball at the top so it will move down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_bc8e1dd2,create a rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_2c5ea0fc,Set up a ramp with a sphere that will tumble down,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_49c14128,insert a bouncy sphere,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_0b35f5b3,introduce a elastic round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_aff73655,include a wood rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_44cdadeb,place a 2.4kg ball on top of a inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_0f55b61a,form a slope and drop a round object on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_0c711dc2,insert a bouncy round object,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_bf44a3de,position a 0.9kg round object in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_2ab94f5d,add a super elastic ball,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_5653cdb0,Build a inclined plane,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_967d702a,create a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_c6925c30,introduce a marble round object and a wood incline,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_35ab8828,"Build a physics scene with a inclined plane, a 2.3kg sphere, and a cube that the sphere will hit",5,complex,complex,"complex,multi-object,interaction",True,True,True
example_fdbc911b,form a sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_12872f46,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_28eeb629,include a wooden round object that will spring back off a metallic block,2,drop,simple,"drop,gravity,falling",False,True,True
example_10672019,Build a inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ff6c1c7d,form a inclined plane and position a 1.4kg rubber round object at the top so it will slide down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_5017c19a,Build a inclined plane,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_65283645,make a sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_54661a5c,insert a bouncy round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_f1921d0c,construct a sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_a4775adc,Set up a slope with a sphere that will roll down,5,complex,complex,"complex,multi-object,interaction",True,True,False
example_20ecf9db,build a round object,2,collision,simple,"collision,impact,momentum",False,True,False
example_ac198033,place a 3.1kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_4e338a5a,create a sphere,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_4c1776ac,Build a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_f3c24e45,Build a inclined plane,5,complex,complex,"complex,multi-object,interaction",True,False,True
example_110bfbbe,make a ball,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_cd78d497,put a 0.6kg ball in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_094ed2fb,add a elastic ball,2,collision,simple,"collision,impact,momentum",False,True,False
example_5d25b363,form a block,1,drop,simple,"drop,gravity,falling",False,False,True
example_0fd9aca0,insert a rubber orb,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_bc7887d3,set a 4.3kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_818596d1,Build a inclined plane,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_3d017cc5,Build a inclined plane,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_40f7337e,build a ball,2,collision,simple,"collision,impact,momentum",False,True,False
example_abc9fc9a,set a 0.5kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_5d8150ca,place a 0.3kg ball in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_8b6a117f,create a sphere,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_194bfe59,place a 1.1kg sphere in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_0e7511ce,place a 1.0kg sphere on top of a inclined plane,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_55f8110a,position a 2.1kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_0a1910d2,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_df82b9d5,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_8e49b98b,create a cube and a sphere next to each other,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_f0f28a28,put a 0.5kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_677859dd,create a orb,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_26c0c156,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_cb48c696,create a block,1,drop,simple,"drop,gravity,falling",False,False,True
example_2185490d,insert a highly elastic round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_decea8f3,introduce a bouncy ball that will spring back off a rocky block,2,drop,simple,"drop,gravity,falling",False,True,True
example_94efcad9,make a box,1,drop,simple,"drop,gravity,falling",False,False,True
example_a136e0b9,put a 2.8kg sphere in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_31363be9,make a incline and place a round object on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_9631e2e8,Build a inclined plane,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_5e143adf,Build a incline,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_2a76e435,place a 1.2kg round object in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_9f97bcb0,set a 3.0kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_c14b3336,build a ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_93e76588,construct a slope and place a sphere on it,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_81e2891a,make a block,1,drop,simple,"drop,gravity,falling",False,False,True
example_f9b88921,include a wooden ball and a timber ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_c788a760,construct a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_e54a09e8,introduce a granite ball and a wood inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_95c3268b,create a scenario where a 3.3kg round object rolls down a wood slope and collides with a block,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_6b79daec,form a slope and position a ball on it,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_63cd14b4,build a rectangular object,2,drop,simple,"drop,gravity,falling",False,True,True
example_4fc3a7cf,insert a metallic round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_19370d73,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_73085402,put a 1.1kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_3a593ca7,Build a inclined plane,4,complex,complex,"complex,multi-object,interaction",True,False,True
example_968c3054,create a ball,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_d6d29baa,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_2b4c6075,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_86512ba3,drop a 1.2kg round object in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_78363958,build a slope and put a orb on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_739ed250,place a 0.8kg orb in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_c1ff071c,build a sphere,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_6adbcc0c,add a bouncy round object and a wooden inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_3929ba40,build a inclined plane and drop a 1.0kg timber ball at the top so it will tumble down due to gravity,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_4385a555,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_bd8bdca8,place a 0.5kg ball on top of a inclined plane,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_e2801274,insert a bouncy box,1,drop,simple,"drop,gravity,falling",False,False,True
example_fcdab35e,Set up a ramp with a sphere that will move down,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_6f05c411,include a plastic ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_65937f05,form a box,1,drop,simple,"drop,gravity,falling",False,False,True
example_e5e67958,include a wooden cube,4,complex,complex,"complex,multi-object,interaction",True,False,True
example_65027244,set a 1.3kg ball in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_3375aaea,insert a rubber sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_3c475daf,introduce a rubber ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_dbeb8d44,place a 1.9kg sphere on top of a incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_0e6a0577,include a metal round object,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_ecf6e2f6,introduce a wood block,1,drop,simple,"drop,gravity,falling",False,False,True
example_a52c53c8,create a round object,2,drop,simple,"drop,gravity,falling",False,True,True
example_5e7db6b0,place a 0.7kg round object on top of a incline,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_a988cef3,add a rubber round object that will rebound off a polymer cube,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_d28c73ea,make a block,1,drop,simple,"drop,gravity,falling",False,False,True
example_6f8b17cd,put a 0.8kg round object in the scene,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_e24eafa0,make a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_60ee5c08,set a 3.8kg sphere in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_f12de8b6,insert a wooden ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_763b9ea8,insert a bouncy orb and a wood inclined plane,4,complex,complex,"complex,multi-object,interaction",True,True,False
example_c30c0ea5,Build a slope,5,complex,complex,"complex,multi-object,interaction",True,False,True
example_7bd4df66,include a bouncy orb and a wood incline,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_0c042f23,position a 0.8kg round object in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_db170372,add a wood sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_b608a551,Build a inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_fd2081d5,include a rubber ball and a timber incline,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_3837ba64,build a orb,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_80cac3c9,place a 1.2kg orb in the scene,2,drop,simple,"drop,gravity,falling",False,True,False
example_fe2ad99f,Set up a inclined plane with a ball that will move down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_9a4fed77,construct a block,1,drop,simple,"drop,gravity,falling",False,False,True
example_3cf56542,place a 1.2kg orb in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_f579b421,Build a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_e4c9901a,insert a metallic orb,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_4b079c21,Set up a incline with a orb that will roll down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_83a709b9,Build a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_1475eee4,put a 1.7kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_f2405033,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_68838dfb,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_a2c45793,Build a inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_4c4a479f,Build a slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_cc7cdf0f,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_f8255a8b,introduce a rubber ball,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_4ffb1d00,make a ball,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_57b273b9,build a incline and drop a sphere on it,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_4ab63c5d,include a wood box,1,drop,simple,"drop,gravity,falling",False,False,True
example_ccbdb88f,include a wood round object and a wood inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_dd1b11d1,drop a 1.5kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_2bea474e,include a timber orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_9c687426,create a ball,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_a74738e9,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_d4c24b98,insert a glass cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_d293b58c,Set up a slope with a sphere that will roll down,7,complex,complex,"complex,multi-object,interaction",True,True,False
example_d9e0b6f5,form a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_257229b9,introduce a polymer orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_487d0fc5,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_eade3be7,set a 2.7kg orb in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_18444699,construct a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_69fd00ce,introduce a wood sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_f95bac43,include a transparent ball and a wooden inclined plane,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_29bdc6c6,build a sphere,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_61b23e83,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_5114c8f5,set a 0.6kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_ac686044,introduce a timber orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_5964c14f,create a round object,2,collision,simple,"collision,impact,momentum",False,True,False
example_084847d0,put a 0.4kg round object in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_b4777434,create a incline and position a 2.1kg plastic orb at the top so it will tumble down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_0572c790,form a ramp and drop a orb on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ec3dbcac,add a super elastic block,3,drop,complex,"drop,gravity,falling",False,True,True
example_c861162a,add a wooden round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_b3f194c8,position a 1.4kg round object in the scene,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_ca0be1b6,add a polymer ball,2,collision,simple,"collision,impact,momentum",False,True,False
example_3fb7c736,insert a steel cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_921e8bf2,add a wooden cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_95cd5d17,create a inclined plane and position a sphere on it,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_ea094546,create a block,1,drop,simple,"drop,gravity,falling",False,False,True
example_4cebc573,build a orb,2,collision,simple,"collision,impact,momentum",False,True,False
example_6276c9ff,create a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_84ba5596,construct a sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_bc53e321,place a 1.9kg sphere in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_7b7e024b,build a orb,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_47bba910,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_767b1b16,place a 0.7kg ball in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_853c8240,include a rubber sphere,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_e441d09c,drop a 1.2kg ball on top of a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_1a9a5831,make a sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_044a671b,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_b93c02b6,place a 0.6kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_944deba4,insert a bouncy round object,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_844dcdf1,create a inclined plane and drop a orb on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_0fe4ef90,drop a 3.9kg orb on top of a inclined plane,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_eb192895,construct a sphere,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_56d70ea7,add a highly elastic round object,5,complex,complex,"complex,multi-object,interaction",True,True,False
example_387cd327,create a rectangular object,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_8fe6d5d0,introduce a glassy sphere,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_5b3faefa,introduce a plastic sphere that will spring back off a plastic box,2,drop,simple,"drop,gravity,falling",False,True,True
example_7fd65bae,Set up a slope with a sphere that will move down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ff4034e9,build a ramp and put a sphere on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_af15a5e3,create a ball,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_a7439850,form a ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_cfd43312,add a frozen sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_1b79658e,include a metal rectangular object,2,drop,simple,"drop,gravity,falling",False,False,True
example_2c816352,introduce a rocky ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_ac48a19c,add a elastic round object,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_08ccae4d,add a rubber sphere and a wood inclined plane,6,complex,complex,"complex,multi-object,interaction",True,True,False
example_33a20cd3,form a inclined plane and set a 0.5kg timber ball at the top so it will slide down due to gravity,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_545c315d,put a 3.0kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_61c8eb32,Set up a ramp with a orb that will move down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_4bde8d36,Set up a slope with a orb that will roll down,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_28fe0c5e,set a 0.7kg orb in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_5d3f69b6,drop a 1.1kg sphere in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_58e0f54c,introduce a bouncy orb that will rebound off a plastic block,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_5f768597,create a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_71c24fb9,add a wooden ball,6,complex,complex,"complex,multi-object,interaction",True,True,False
example_9d240516,position a 2.5kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_79f9a757,add a super elastic sphere that will bounce off a timber box,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_b0488c8d,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_c8ee8dcc,place a 1.1kg round object in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_6a99aef9,include a metallic round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_c4ab1375,Build a inclined plane,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_e7a3bfd7,include a rubber ball,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_ec6df559,add a bouncy orb and a wood ramp,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_c23babd3,set a 1.3kg orb in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_ea697fb6,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_ea890fd9,Build a incline,4,complex,complex,"complex,multi-object,interaction",True,True,False
example_a58a3cac,build a sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_7cfead76,Build a inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_b1e7254d,construct a incline and drop a 2.8kg rubber sphere at the top so it will move down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_770e9965,add a granite ball that will bounce off a slippery box,2,drop,simple,"drop,gravity,falling",False,True,True
example_2b94046f,create a round object,2,drop,simple,"drop,gravity,falling",False,True,False
example_82104e6a,add a plastic orb,2,collision,simple,"collision,impact,momentum",False,True,False
example_81d4ab87,position a 1.0kg sphere on top of a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_81cdd499,include a elastic ball,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_ef9aca03,introduce a highly elastic ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_fe9501b3,insert a highly elastic ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_074b0867,drop a 1.9kg round object on top of a slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_d2e6eac5,add a iron sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_4cce8cbe,build a orb,2,drop,simple,"drop,gravity,falling",False,True,True
example_419eaeb6,insert a glassy rectangular object,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_a58b3da8,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_1214d0cb,Build a incline,6,complex,complex,"complex,multi-object,interaction",True,False,True
example_af3f4160,include a polymer sphere and a timber ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_162ca752,form a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_dcae41cc,put a 3.1kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_90530e06,introduce a wooden ball and a wooden inclined plane,5,complex,complex,"complex,multi-object,interaction",True,True,False
example_119524c6,drop a 1.2kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_c7cac112,Build a slope,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_605d3c60,build a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_0605fe77,create a ramp and place a orb on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_e381e2e9,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_45a84862,construct a incline and position a 2.0kg wooden ball at the top so it will slide down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_6b0f9c49,include a elastic cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_33430e18,insert a super elastic round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_a4c91342,create a ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_5e77a9c5,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_55d754dc,place a 1.0kg sphere in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_8ccc65e3,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_b887b8b7,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_38c252bf,make a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_7c761265,make a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_1d8ab046,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_fa0e13c6,create a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_c78c0b04,create a rectangular object and a orb next to each other,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_fa0fda43,form a sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_230ff373,set a 1.0kg ball in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_c2cc7f90,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_72f4cb08,build a ball,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_78306d79,set a 1.9kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_c6be5093,add a wooden orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_a97c7023,create a orb,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_0556f037,add a iron orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_6bcbbf09,build a box and a round object next to each other,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_ed3be27b,introduce a icy round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_fc8a4917,put a 0.8kg sphere in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_cf7f51e9,create a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_2ffbf9d5,place a 1.0kg orb on top of a inclined plane,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_13c16a95,position a 2.9kg round object in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_253fed80,insert a bouncy round object,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_9fe81863,build a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_e6cb7ef3,Build a inclined plane,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_95d318fa,add a highly elastic round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_cd974339,insert a plastic block,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_d0f8b836,build a ball,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_4c9be39b,Build a incline,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_d54a36a8,create a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_b5614dba,build a ball,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_571a5191,add a wood ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_0f1c5892,insert a bouncy ball,2,collision,simple,"collision,impact,momentum",False,True,False
example_496ea275,place a 4.4kg orb in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_cd645618,put a 1.8kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_db792812,create a orb,2,drop,simple,"drop,gravity,falling",False,True,False
example_c43e3f7b,Build a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_82511f53,create a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_f68f9c37,include a highly elastic sphere,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_c6371ce2,make a cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_41ad139d,include a metal orb and a timber inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_c3238a4b,place a 1.4kg orb on top of a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_749ce15d,put a 0.9kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_0c44434f,build a block,5,complex,complex,"complex,multi-object,interaction",True,False,True
example_f15b11e1,introduce a wooden sphere,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_593e2253,introduce a plastic orb that will bounce off a metallic block,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_afa36d3b,construct a ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_2fc044e3,build a round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_bb843fca,introduce a metal sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_bd493dd4,add a rubber round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_567e2bd5,introduce a elastic round object and a wood inclined plane,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_00991f54,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_32f4d7be,form a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_43b75133,Set up a incline with a sphere that will slide down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_bb6fb1d5,build a inclined plane and set a 1.0kg steel round object at the top so it will slide down due to gravity,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_10dc9d06,insert a glassy sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_8ab2db8a,create a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_7641add3,drop a 0.8kg round object in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_129d9253,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_1f8cb895,Set up a ramp with a ball that will slide down,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_eb3711c3,Build a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_4d5cc2be,build a ball,2,drop,simple,"drop,gravity,falling",False,True,True
example_11be0eca,set a 1.3kg round object in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_204bcaa5,include a plastic sphere and a timber ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_b6bfb84e,insert a bouncy box,3,drop,complex,"drop,gravity,falling",False,True,True
example_7f655b5c,construct a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_29cab02e,set a 1.6kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_44f3e020,Set up a ramp with a sphere that will move down,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_6152fc94,add a super elastic orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_94bda9f3,place a 0.9kg ball on top of a slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_bdea848e,make a block,1,drop,simple,"drop,gravity,falling",False,False,True
example_9cdf6cbb,include a rubber ball,2,drop,simple,"drop,gravity,falling",False,True,True
example_9418ae5c,construct a sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_4f5dfcd9,construct a incline and set a 2.7kg glassy sphere at the top so it will slide down due to gravity,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_572caa96,Set up a slope with a sphere that will roll down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_7375da77,Set up a incline with a sphere that will roll down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_4ffdf938,drop a 1.9kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_60354340,build a round object,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_7310dc75,Build a incline,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_1936e7d2,set a 2.3kg round object in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_b54075d0,form a ramp and position a round object on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_108138a5,introduce a stone sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_42ed5e8b,add a highly elastic ball,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_aef6c6a2,insert a wooden rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_b8f8ecae,place a 1.1kg orb in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_2d75a0bb,drop a 4.6kg orb in the scene,2,drop,simple,"drop,gravity,falling",False,True,True
example_8beb40ca,make a ball,2,collision,simple,"collision,impact,momentum",False,True,False
example_7039f5d4,introduce a polymer sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_4f7a44fe,Build a inclined plane,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_9a499290,create a orb,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_b22491a8,make a box and a sphere next to each other,3,drop,complex,"drop,gravity,falling",False,True,True
example_c67b8d96,drop a 1.3kg sphere in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_6c2dd01f,build a round object,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_eb7dabb5,create a ball,2,drop,simple,"drop,gravity,falling",False,True,False
example_0d652897,put a 0.5kg sphere in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_f91f877c,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_19f8cec5,set a 0.9kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_8cba38b0,Set up a inclined plane with a round object that will roll down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_90b812aa,drop a 0.8kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_bf307fdf,place a 1.9kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_550c5434,add a iron round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_416c5f8a,add a wooden ball and a wooden slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ed6e36ba,make a orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_6dc2601a,build a inclined plane and put a round object on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_1ca31849,include a rubber orb,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_cac18a5b,make a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_93514dda,make a slope and drop a 3.6kg frozen ball at the top so it will tumble down due to gravity,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_5db7971c,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_dfa8b431,add a rubber round object,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_e199ecb2,introduce a highly elastic sphere and a wooden inclined plane,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_83d920cd,"Build a physics scene with a inclined plane, a 2.1kg sphere, and a cube that the sphere will hit",4,complex,complex,"complex,multi-object,interaction",True,True,True
example_5afd1b80,introduce a stone sphere,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_31f9b617,Build a slope,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_19fda1ff,introduce a rubber orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_d7fa0a82,add a bouncy sphere,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_e0776177,"Build a physics scene with a incline, a 3.6kg orb, and a box that the orb will hit",6,complex,complex,"complex,multi-object,interaction",True,True,True
example_ad1ec93f,position a 1.5kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_2f416645,create a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_70daf723,build a ball,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_7f565fda,make a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_63fca9a0,create a scenario where a 1.4kg round object rolls down a wood slope and collides with a cube,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_bb28d7be,insert a elastic ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_be7de51e,form a orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_2946c260,make a orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_4f752793,drop a 4.5kg round object in the scene,3,drop,complex,"drop,gravity,falling",False,True,False
example_8b1e827a,position a 2.2kg orb in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_1accaf74,create a round object,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_2ac1891f,form a cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_8b399e8d,form a orb,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_d09ec9b5,make a ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_8b135650,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_5b311110,construct a scenario where a 2.4kg sphere rolls down a wooden ramp and collides with a rectangular object,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_eb6b5be1,insert a plastic round object,2,collision,simple,"collision,impact,momentum",False,True,False
example_cf8fe5ef,make a ramp and place a 1.6kg plastic round object at the top so it will roll down due to gravity,7,complex,complex,"complex,multi-object,interaction",True,True,False
example_1ba0dcbb,include a bouncy round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_df85ee2c,insert a bouncy orb,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_75943039,insert a highly elastic sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_e342becf,construct a ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_83e33730,"Build a physics scene with a ramp, a 1.8kg orb, and a block that the orb will hit",6,complex,complex,"complex,multi-object,interaction",True,True,True
example_e2f4107d,build a sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_22054bc9,place a 1.3kg orb on top of a inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_91748124,insert a timber rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_edd77182,create a incline and place a sphere on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_da56821a,add a rubber ball,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_0d934e08,create a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_9a4a9a41,add a granite sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_f8736e7c,set a 1.5kg ball in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_071c5b47,drop a 0.8kg sphere in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_71d5612b,place a 2.3kg ball in the scene,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_d76d7097,construct a orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_440159c3,build a block and a sphere next to each other,2,drop,simple,"drop,gravity,falling",False,True,True
example_375c4ba0,insert a polymer ball that will bounce off a stone rectangular object,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_9e311579,place a 3.8kg sphere on top of a ramp,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_868bf8d2,add a wood ball that will spring back off a timber block,2,drop,simple,"drop,gravity,falling",False,True,True
example_72c06e2e,create a slope and position a ball on it,4,complex,complex,"complex,multi-object,interaction",True,True,False
example_3e37ada1,Build a inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_afe85bb6,drop a 0.6kg round object in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_07f0b5e8,introduce a steel orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_7cad3d83,introduce a wooden orb that will bounce off a rocky cube,2,drop,simple,"drop,gravity,falling",False,True,True
example_006e27c9,include a polymer orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_d7949f6c,make a orb,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_56cf270e,form a slope and drop a round object on it,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_ffd290c6,add a super elastic round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_3894f87e,construct a inclined plane and drop a 1.2kg wood round object at the top so it will slide down due to gravity,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_20302856,add a metallic rectangular object,4,complex,complex,"complex,multi-object,interaction",True,False,True
example_8bd0a9d3,form a inclined plane and drop a 1.2kg steel sphere at the top so it will roll down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_93c21422,set a 0.6kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_7648b405,Set up a slope with a round object that will roll down,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_b45251e4,add a wooden sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_328db60e,insert a elastic round object that will spring back off a wooden cube,2,drop,simple,"drop,gravity,falling",False,True,True
example_8bb8dab2,Build a inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_71a6ff8c,create a slope and place a sphere on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_b70664ae,put a 0.8kg ball in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_755d7e71,include a metallic ball,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_8042e8aa,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_f642fb46,create a orb,3,drop,complex,"drop,gravity,falling",False,True,False
example_90f5c82d,create a round object,2,drop,simple,"drop,gravity,falling",False,True,True
example_3d70108a,include a elastic ball,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_115c155a,create a round object,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_bb2b9aa1,drop a 3.4kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_1594c92d,create a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_769e571f,set a 0.7kg orb in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_82641394,create a incline and set a 1.7kg glass ball at the top so it will tumble down due to gravity,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_b6459a95,introduce a wooden orb and a wooden slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_2e949903,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_dfda74a4,introduce a stone orb,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_d1e2644a,position a 4.9kg round object in the scene,3,drop,complex,"drop,gravity,falling",False,True,True
example_bfe70dc1,place a 0.8kg orb in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_60b62944,create a box,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_48f8252d,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_04f6ff08,put a 1.7kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_0eba6d89,place a 1.8kg orb in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ed7a3ed8,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_da2d4416,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_d4ac9af1,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_ea554165,create a orb,2,collision,simple,"collision,impact,momentum",False,True,False
example_2866189c,form a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_ce7e6fe8,insert a rubber sphere,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_59cb9fe3,include a rubber ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_ea6adaf9,include a plastic round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_7e9d9fab,insert a wood block,6,complex,complex,"complex,multi-object,interaction",True,False,True
example_a66b5f15,create a orb,2,collision,simple,"collision,impact,momentum",False,True,False
example_e71c2f21,Build a slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_2a56bfb7,construct a inclined plane and drop a 1.8kg metallic ball at the top so it will move down due to gravity,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_da040b03,form a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_19a00e3a,insert a bouncy sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_223a6adf,create a incline and drop a 0.7kg plastic sphere at the top so it will slide down due to gravity,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_7f653b89,insert a bouncy orb,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_ec73255e,include a wooden rectangular object,2,drop,simple,"drop,gravity,falling",False,True,True
example_1752fe90,form a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_d6d4acd8,insert a elastic ball,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_826d46ca,construct a ramp and position a 3.0kg rubber ball at the top so it will roll down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_5a67de80,construct a orb,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_3310b9c6,insert a iron orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_d62a00b1,place a 1.0kg orb in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_55d52f4b,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_86d1c63c,introduce a wood orb,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_fd3b6007,build a slope and put a 1.9kg wood orb at the top so it will slide down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_063c6538,insert a wood orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_ec31f54a,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_f2796281,set a 0.9kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_593e22e5,add a highly elastic round object,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_b1013364,introduce a wooden ball and a wooden ramp,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_579d31c4,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_65537ab8,add a timber ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_030ccd4a,Build a incline,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_8468acb9,introduce a metallic orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_caa3c8ed,add a polymer round object,2,collision,simple,"collision,impact,momentum",False,True,False
example_9f969f0d,"Build a physics scene with a slope, a 1.0kg ball, and a box that the ball will hit",4,complex,complex,"complex,multi-object,interaction",True,True,True
example_9e016c27,put a 1.4kg round object in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_94cc269b,place a 1.7kg sphere in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_bc134e99,create a slope and position a sphere on it,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_11176f55,add a wooden orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_a1572ae3,add a rubber round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_eea8dba7,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_2415fd59,add a timber ball that will spring back off a rocky block,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_cd708d90,add a iron round object and a timber incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_42478869,build a sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_771944e6,introduce a wooden round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_d580ce51,include a rubber orb,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_2e66ffbf,build a rectangular object and a ball next to each other,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_eb45830c,form a slope and drop a ball on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ead47d60,make a orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_83ff5678,build a round object,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_b691bd2f,place a 1.3kg round object in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_c5f91db2,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_8baea5ff,build a block,5,complex,complex,"complex,multi-object,interaction",True,False,True
example_2da78cc6,set a 1.5kg ball in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_37d9f914,build a rectangular object and a orb next to each other,2,drop,simple,"drop,gravity,falling",False,True,True
example_fe6dd20c,insert a iron orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_f9d4cf32,Build a incline,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_dc76936c,construct a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_4526449c,position a 1.3kg sphere in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_49390fb1,add a highly elastic ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_153210fe,create a block and a orb next to each other,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_6ad94c05,form a ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_dcd9dae7,add a wood orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_fe14adec,add a elastic round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_fe3c673f,Set up a incline with a sphere that will slide down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_26af264d,build a incline and set a orb on it,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_96c2b46f,build a orb,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_5d16932f,build a incline and set a orb on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ce586d43,Build a incline,5,complex,complex,"complex,multi-object,interaction",True,False,True
example_b4e1133f,set a 4.9kg orb in the scene,2,drop,simple,"drop,gravity,falling",False,True,True
example_6980652a,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_94044cbf,build a inclined plane and put a sphere on it,5,complex,complex,"complex,multi-object,interaction",True,True,False
example_93d8931c,drop a 0.4kg ball in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_fb83df6c,insert a marble orb and a wood inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_4ce04d9d,construct a incline and set a 2.1kg rubber ball at the top so it will roll down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_8eba665d,place a 0.7kg round object on top of a incline,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_4992ca3a,set a 0.5kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_978166d5,set a 1.0kg orb in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_456c4695,"Build a physics scene with a incline, a 1.0kg sphere, and a rectangular object that the sphere will hit",7,complex,complex,"complex,multi-object,interaction",True,True,True
example_27f50b99,position a 1.3kg ball in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_40e8a208,set a 2.9kg orb in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_cdd9d32f,position a 3.0kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_e2170057,set a 3.0kg ball in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_7f38de5e,place a 2.6kg sphere on top of a incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_88fd3c5b,introduce a timber orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_e9b66bf4,add a polymer ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_fcffeeb4,Build a ramp,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_88b30710,Build a incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_6b26740e,build a orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_3fe55d5a,form a orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_79c1a8f1,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_b53888bc,build a slope and set a 1.4kg wooden round object at the top so it will move down due to gravity,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_19bb336e,Set up a ramp with a sphere that will slide down,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_155b05ad,position a 0.5kg sphere in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_969cafc4,create a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_9f851bd4,make a orb,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_3a9c641f,Build a incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_24ece10c,Build a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_1cf99eb8,make a scenario where a 3.7kg round object rolls down a wooden ramp and collides with a cube,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_0c20aa30,Build a inclined plane,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_b0abf9fb,include a stone ball,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_3a1f4ba3,build a sphere,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_cf128122,place a 1.9kg sphere in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_50f6b20e,build a incline and position a ball on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_6d0d282f,make a rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_e2b237d8,place a 0.7kg ball in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_f33cdb64,include a bouncy sphere,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_221eb147,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_d479434a,create a ramp and put a 2.3kg rocky ball at the top so it will move down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_cc7d1e93,place a 1.8kg orb on top of a ramp,7,complex,complex,"complex,multi-object,interaction",True,True,False
example_e95e952e,set a 1.4kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_abf1cec1,add a rubber round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_252ee75e,drop a 1.7kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_e33160e0,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_ba122d45,add a rubber ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_a7418cd2,position a 0.8kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_a3e93f83,construct a scenario where a 2.2kg round object rolls down a wood ramp and collides with a rectangular object,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_46e82727,create a round object,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_0b87f2b4,construct a orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_ef22570e,position a 1.4kg ball in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_afb15f7c,make a ball,2,drop,simple,"drop,gravity,falling",False,True,True
example_7f3a31a6,Build a inclined plane,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_892ddf52,set a 4.7kg orb in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_9937a1f7,position a 0.8kg ball in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_41537b20,create a orb,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_6c742b02,position a 4.8kg round object in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_488369dc,include a plastic round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_8fb749de,create a orb,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_6402015e,Build a incline,4,complex,complex,"complex,multi-object,interaction",True,False,True
example_aa31e01b,insert a elastic ball and a wood incline,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_63672770,create a block and a round object next to each other,2,drop,simple,"drop,gravity,falling",False,True,True
example_fad9f492,place a 2.2kg orb on top of a incline,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_1fce6dfb,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_b2bb4fe6,position a 3.7kg orb in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_e97dbf8c,build a sphere,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_08945b54,put a 0.6kg sphere in the scene,3,drop,complex,"drop,gravity,falling",False,True,True
example_40ce7a54,put a 0.7kg sphere in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_8071c50c,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_37abc526,introduce a plastic sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_152d7fb4,place a 5.0kg round object in the scene,2,drop,simple,"drop,gravity,falling",False,True,False
example_3144cbc9,add a granite sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_a00e8abc,drop a 1.0kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_ea39a06d,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_b8db5bdd,construct a orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_200a0b0e,add a bouncy round object,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_aaf0be0c,place a 1.4kg orb in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_a4f81bde,Build a incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_87400992,position a 2.6kg round object in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_ade7b6c8,make a sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_1e9e92c2,Build a incline,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_66d2e568,form a ball,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_a7c88ee5,drop a 2.7kg ball in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_e1e4ea85,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_24df0e67,construct a slope and drop a 2.0kg elastic ball at the top so it will move down due to gravity,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_c4f802c3,introduce a rubber ball,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_6cba5017,introduce a wood sphere that will rebound off a glass block,2,drop,simple,"drop,gravity,falling",False,True,True
example_ab66da9b,drop a 2.3kg orb in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_3233d2df,Set up a ramp with a sphere that will roll down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_5158baf1,construct a ramp and place a orb on it,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_4a206c27,make a cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_1adb0128,add a timber sphere and a timber inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_c6fff892,form a ramp and position a 1.3kg glass sphere at the top so it will move down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_3bfebe6e,put a 4.8kg orb in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_3ebfb202,create a ball,6,complex,complex,"complex,multi-object,interaction",True,True,False
example_f3ef4e42,create a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_4ce6466e,Set up a ramp with a sphere that will roll down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_1edf4362,make a ball,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_2b3f5fe1,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_2742ee13,introduce a marble orb and a wood slope,6,complex,complex,"complex,multi-object,interaction",True,True,False
example_e1226146,place a 0.9kg sphere in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_00f01808,include a metal round object,2,drop,simple,"drop,gravity,falling",False,True,False
example_a8079be3,create a ball,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_c43be6f2,put a 1.0kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_144a8f83,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_9df42290,create a orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_fe7cadc0,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_84ed3e01,introduce a rocky sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_e31efa6d,create a box,2,drop,simple,"drop,gravity,falling",False,True,True
example_2d1c0db3,Set up a slope with a sphere that will roll down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_5f02e52e,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_056e7ef2,introduce a glass rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_b12b8bce,include a bouncy sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_477b1587,insert a marble orb and a wood inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_bc6bfb5e,form a ball,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_d9f5d799,create a ball,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_5afde62a,set a 2.3kg round object on top of a ramp,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_cfff90de,construct a incline and place a orb on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_68d41a08,Build a incline,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_c3614f45,create a round object,3,drop,complex,"drop,gravity,falling",False,True,True
example_eaccbafd,create a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_1990b3ef,build a orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_963bae09,drop a 2.9kg round object in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_8d225c57,create a sphere,2,drop,simple,"drop,gravity,falling",False,True,True
example_1f1a3485,Build a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_1681b166,build a ramp and place a sphere on it,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_bbe684ee,introduce a plastic box,5,complex,complex,"complex,multi-object,interaction",True,False,True
example_63f214db,place a 1.9kg sphere in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_411bd603,introduce a rubber box,1,drop,simple,"drop,gravity,falling",False,False,True
example_5c728d36,form a ramp and place a orb on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_88bc2802,include a bouncy round object,2,collision,simple,"collision,impact,momentum",False,True,False
example_202f434d,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_820763bf,add a timber round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_b807186c,make a cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_c7c3d4c9,insert a polymer sphere and a timber ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_78be4cab,Build a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_954277a4,set a 1.0kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_9ebb3050,Build a ramp,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_d7fc6933,Build a incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_f4bf1a62,set a 1.1kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_43ec3c52,Build a incline,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_cd6830f6,drop a 1.0kg round object in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_3d15633e,add a wood orb,2,drop,simple,"drop,gravity,falling",False,True,False
example_252c6c7e,Build a slope,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_443692c7,introduce a bouncy block,1,drop,simple,"drop,gravity,falling",False,False,True
example_697b218b,put a 1.4kg ball in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_5c150b8b,include a wood ball and a wood ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_f6a63b3f,introduce a elastic round object that will bounce off a polymer cube,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_2e4b4052,add a plastic orb and a timber slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_110c91f2,create a rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_3ed749b6,construct a inclined plane and place a 1.8kg polymer orb at the top so it will roll down due to gravity,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_db33e665,add a elastic orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_0e5361f2,include a metallic orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_dcd65e2b,drop a 0.7kg orb in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_0f1a7913,Set up a inclined plane with a ball that will roll down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_6e62e70d,set a 2.0kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_2711ded0,construct a sphere,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_b77358e1,drop a 1.9kg round object in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_13da4d2c,include a granite box,1,drop,simple,"drop,gravity,falling",False,False,True
example_32fc4e67,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_14ae9f6c,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_6a85dfd8,add a timber orb,3,drop,complex,"drop,gravity,falling",False,True,False
example_c6fa1450,add a highly elastic round object,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_d2e7c530,add a highly elastic sphere,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_18b89a94,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_f26a25bc,put a 1.3kg sphere in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_89eedea6,add a metal ball,2,drop,simple,"drop,gravity,falling",False,True,False
example_621094ec,introduce a wood round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_c261f8db,Build a incline,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_ca5c664b,insert a rubber round object,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_a4963f73,construct a scenario where a 3.1kg round object rolls down a wooden inclined plane and collides with a rectangular object,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_44c3346c,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_fdf98c40,create a orb,2,collision,simple,"collision,impact,momentum",False,True,False
example_3299090b,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_55b68e78,Build a inclined plane,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_9e043ae6,make a rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_d6f6c403,form a orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_436f3a77,build a rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_9e524b2a,Build a incline,4,complex,complex,"complex,multi-object,interaction",True,False,False
example_651d25b2,drop a 1.4kg sphere on top of a ramp,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_8b67c080,Build a inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_cfce0f6e,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_ef2477dc,place a 2.4kg orb in the scene,2,drop,simple,"drop,gravity,falling",False,True,True
example_d2811de1,include a metal sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_f5aa4b84,add a wood box,2,drop,simple,"drop,gravity,falling",False,True,True
example_b956a13f,add a polymer round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_c39fda02,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_c53b1a67,position a 1.4kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_75904bfc,construct a inclined plane and place a 0.8kg polymer sphere at the top so it will move down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_49e36608,drop a 1.7kg ball in the scene,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_1001ce45,construct a incline and drop a sphere on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_11a73f32,insert a wooden sphere and a timber ramp,7,complex,complex,"complex,multi-object,interaction",True,True,False
example_e9b05fc5,Build a ramp,7,complex,complex,"complex,multi-object,interaction",True,True,False
example_a47f5270,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_e3c1b5ff,make a cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_32aa7429,build a incline and position a 2.4kg steel orb at the top so it will tumble down due to gravity,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_0bfcb0c5,set a 0.7kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_b93a7760,position a 0.4kg ball in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_824a4ca6,create a orb,2,drop,simple,"drop,gravity,falling",False,True,False
example_eecfb89b,drop a 1.0kg sphere in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_7668d3ad,introduce a elastic orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_1ee1c623,introduce a super elastic ball,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_c43abd64,add a glass sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_087d5935,introduce a plastic block,1,drop,simple,"drop,gravity,falling",False,False,True
example_6f42bf05,include a wooden box,2,drop,simple,"drop,gravity,falling",False,True,True
example_0d46b46d,position a 4.8kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_7a8ab97a,drop a 4.7kg round object in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_223f4b35,Set up a inclined plane with a orb that will roll down,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_dc8bdef2,introduce a wood orb and a timber ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_37f9950a,create a ball,3,drop,complex,"drop,gravity,falling",False,True,False
example_0ec2ad45,place a 0.5kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_827ddbb4,make a incline and drop a 2.9kg granite ball at the top so it will slide down due to gravity,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_07e16e21,build a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_6f2fc9b4,build a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_91e4b6fd,build a block,2,drop,simple,"drop,gravity,falling",False,True,True
example_1937fb1e,drop a 0.7kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_a90ed2f5,put a 1.3kg ball in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_c7db0c2d,include a glass round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_6e3148a2,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_374a32a5,drop a 1.2kg ball in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_94229c23,include a timber orb and a wooden ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_a9b83c18,Build a slope,5,complex,complex,"complex,multi-object,interaction",True,False,False
example_24bb16ac,construct a cube and a ball next to each other,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_2d962353,create a ball,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_615dbb8b,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_ad12997d,place a 4.1kg ball in the scene,3,drop,complex,"drop,gravity,falling",False,True,False
example_2aaf022c,add a transparent orb,2,drop,simple,"drop,gravity,falling",False,True,True
example_ad614ac3,drop a 2.9kg round object in the scene,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_c488cc9f,drop a 0.8kg round object on top of a inclined plane,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_267f141e,construct a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_e3c44c09,drop a 0.5kg ball in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_43505c78,Set up a ramp with a ball that will slide down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_c05bbb9c,Build a inclined plane,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_922c6521,place a 0.8kg ball in the scene,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_726542c3,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_546c4dda,introduce a plastic orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_0752beca,put a 0.9kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_20705a56,Build a ramp,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_ea28fb12,set a 1.3kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_810f648e,drop a 0.7kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_e5767e80,create a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_6fef4b4f,build a slope and drop a sphere on it,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_2ca149e9,drop a 1.4kg orb on top of a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_9fbc8d89,introduce a plastic round object and a wood inclined plane,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_0e15d45f,create a scenario where a 1.3kg orb rolls down a timber slope and collides with a box,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_db6716bc,make a sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_260fb626,introduce a steel round object and a wooden slope,4,complex,complex,"complex,multi-object,interaction",True,True,False
example_e2d8d041,Build a incline,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_fd402a5c,introduce a bouncy orb,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_6f3daf90,create a box and a ball next to each other,2,drop,simple,"drop,gravity,falling",False,True,True
example_790d8bf7,introduce a bouncy round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_e273af01,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_4c585f11,place a 2.5kg ball in the scene,2,drop,simple,"drop,gravity,falling",False,True,False
example_615577df,introduce a plastic round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_29c55eb6,introduce a plastic sphere,3,drop,complex,"drop,gravity,falling",False,True,False
example_2e249195,create a ball,2,drop,simple,"drop,gravity,falling",False,True,True
example_f8c68e25,Set up a ramp with a ball that will slide down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_cb22030a,drop a 0.9kg orb in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_f5e7cbd3,build a scenario where a 3.1kg sphere rolls down a timber inclined plane and collides with a box,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_41dea6a4,insert a bouncy orb,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_6fc93b73,set a 1.3kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_e5838842,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_114cb339,place a 1.2kg round object in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_09cec9ec,Build a ramp,6,complex,complex,"complex,multi-object,interaction",True,True,False
example_ebdd04e2,position a 0.7kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_34bc956f,build a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_63530950,introduce a elastic sphere and a wood inclined plane,4,complex,complex,"complex,multi-object,interaction",True,True,False
example_c4b42e0e,form a inclined plane and position a 2.9kg metal round object at the top so it will tumble down due to gravity,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_c762a313,position a 2.7kg sphere in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_6271630c,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_9cd72dd5,introduce a bouncy sphere,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_7c6441c8,"Build a physics scene with a ramp, a 3.9kg orb, and a cube that the orb will hit",7,complex,complex,"complex,multi-object,interaction",True,True,True
example_a4aaa429,Build a inclined plane,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_4a2ed9fd,include a timber sphere and a wood ramp,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_20db1b47,add a icy round object,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_d412bcb4,include a marble sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_cd0f4aad,add a rocky orb that will spring back off a wooden box,2,drop,simple,"drop,gravity,falling",False,True,True
example_52f1c1b0,build a sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_6814492c,position a 0.8kg ball in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_423e4cfe,build a round object,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_cb1edb0e,place a 1.0kg ball in the scene,2,drop,simple,"drop,gravity,falling",False,True,True
example_5d9db42b,Build a incline,5,complex,complex,"complex,multi-object,interaction",True,True,False
example_63e4631c,Set up a inclined plane with a orb that will roll down,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_fd069370,create a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_e09fd6a7,create a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_26b6e299,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_68a95470,place a 1.7kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_65c2cd72,make a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_5a520c47,introduce a wooden block,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_5f882e6f,add a elastic rectangular object,5,complex,complex,"complex,multi-object,interaction",True,False,True
example_81bd8e9a,create a cube and a orb next to each other,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_86bdb259,Build a ramp,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_a4ba2b39,create a ball,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_1519bc7f,Build a slope,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_1e298c5e,add a elastic cube,4,complex,complex,"complex,multi-object,interaction",True,False,True
example_ae120ac5,construct a ramp and place a 0.6kg rubber sphere at the top so it will move down due to gravity,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_47e926ba,build a sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_a293ae24,set a 3.8kg round object in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_cb16a925,add a elastic ball that will bounce off a wooden rectangular object,2,drop,simple,"drop,gravity,falling",False,True,True
example_a8838d8b,Build a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_a57a7318,add a frozen orb,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_8fd3285c,set a 1.3kg orb in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_9bf2f8ac,build a cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_6b75ed32,add a highly elastic sphere and a wooden slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_3330d8cf,introduce a rocky sphere and a wooden incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_3912bbe4,Build a slope,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_f6619920,place a 0.7kg orb in the scene,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_6e7ac51e,put a 1.7kg orb in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_ae0a593f,build a ball,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_f14d3f5d,include a rubber orb,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_38c18351,place a 1.1kg round object in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_7ad05dbb,drop a 0.5kg round object in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_9bbf86a9,place a 1.1kg round object in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_8d90cf45,drop a 1.3kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_7df87c6a,make a box and a sphere next to each other,3,drop,complex,"drop,gravity,falling",False,True,True
example_f42032cf,drop a 2.7kg ball in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_30aad671,place a 2.6kg round object in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_4c42ca53,set a 1.4kg sphere in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_fa1b28e8,drop a 1.9kg sphere in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_be7d650d,Set up a slope with a orb that will roll down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_26c2b2b5,insert a wood round object and a timber incline,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_4bfea081,Build a slope,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_76de0931,introduce a metallic sphere and a timber ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_608ac864,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_7f84ab8d,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_0c7daabe,build a round object,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_0c8e704a,Build a incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_8e824c50,make a ramp and position a sphere on it,6,complex,complex,"complex,multi-object,interaction",True,True,False
example_0c4c9fa3,place a 1.7kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_fe6af076,set a 1.0kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_0b8398ca,place a 0.9kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_31d8f8ce,construct a orb,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_6f1d7f1f,create a inclined plane and position a 2.2kg rubber orb at the top so it will move down due to gravity,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_d4e801e3,include a timber rectangular object,1,drop,simple,"drop,gravity,falling",False,False,True
example_9943c814,insert a ice orb and a timber slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_bbe196a5,position a 0.5kg ball in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_d1abd0d8,add a wood orb,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_4674e607,Build a slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_2054e8da,place a 0.6kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_4548028b,add a plastic sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_701428b8,make a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_6ebdd508,Build a ramp,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_08c03b26,create a incline and put a ball on it,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_6963e0a2,drop a 0.8kg ball in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_66d2fe71,introduce a bouncy box,5,complex,complex,"complex,multi-object,interaction",True,False,True
example_89551997,include a granite orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_600f52ea,include a wooden orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_2c0833aa,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_15e3b289,Build a ramp,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_eb69f5c9,Build a incline,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_85245892,build a slope and put a 0.6kg rubber round object at the top so it will move down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_a81799ee,include a bouncy sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_e1ca3062,Build a incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_3adda267,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_089206d1,create a incline and put a 0.8kg metallic ball at the top so it will roll down due to gravity,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_4bb9c60d,set a 1.4kg sphere in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_5d509c82,build a ramp and drop a 2.4kg rubber sphere at the top so it will move down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_09878eb8,build a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_22898841,include a elastic ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_92ce870f,make a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_6642c9af,include a icy ball and a wood slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_84eb7aba,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_7eb877f2,include a rubber block,1,drop,simple,"drop,gravity,falling",False,False,True
example_ade5527b,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_5262b40f,create a sphere,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_450cca5d,make a ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_49001535,construct a block,1,drop,simple,"drop,gravity,falling",False,False,True
example_5559123e,create a ball,2,collision,simple,"collision,impact,momentum",False,True,False
example_960f0943,build a ball,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ae57405b,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_6b610a7d,Build a slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_8f8b7435,introduce a iron sphere and a wooden ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_84263c0c,build a box and a round object next to each other,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_e67a0d99,insert a bouncy orb,2,drop,simple,"drop,gravity,falling",False,True,False
example_6bd08591,include a metallic sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_8798f346,drop a 1.6kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_fdac56f4,set a 3.9kg round object in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_a388e64b,construct a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_7cab06eb,insert a rubber orb,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_837f7a3d,include a rubber orb,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_9ed067a3,create a orb,2,collision,simple,"collision,impact,momentum",False,True,False
example_3ab52287,set a 0.7kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_f3a89b53,drop a 1.0kg sphere in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_5c82fd60,set a 1.4kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_7fd6d564,put a 1.2kg orb in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_07731029,drop a 0.5kg ball in the scene,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_e5438966,construct a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_e35ce901,"Build a physics scene with a inclined plane, a 1.6kg sphere, and a block that the sphere will hit",5,complex,complex,"complex,multi-object,interaction",True,True,True
example_5f180e6c,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_7c6a561b,include a glass round object and a wood ramp,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_6a619cc4,insert a elastic round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_3b86bc5f,include a wood ball,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_7fc583d2,Build a incline,7,complex,complex,"complex,multi-object,interaction",True,True,False
example_aa4b6697,make a cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_00fc4d17,Set up a incline with a orb that will roll down,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_e3cbe2bb,add a elastic ball,2,collision,simple,"collision,impact,momentum",False,True,False
example_0ac7a593,add a elastic round object,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_4f16856d,place a 0.9kg orb in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_4af98fcb,introduce a frozen sphere and a wood incline,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_7d17f6a6,drop a 1.3kg orb in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_206b87cd,add a highly elastic sphere,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_bee1c173,build a box and a orb next to each other,2,drop,simple,"drop,gravity,falling",False,True,True
example_b2153c70,build a round object,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_2ddf72c7,build a orb,3,drop,complex,"drop,gravity,falling",False,True,False
example_964ddaf0,form a inclined plane and place a sphere on it,5,complex,complex,"complex,multi-object,interaction",True,True,False
example_31174f49,place a 1.0kg orb in the scene,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_de7c4f88,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_536fab25,build a ball,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_3f4fb1ec,Build a slope,4,complex,complex,"complex,multi-object,interaction",True,False,False
example_beb723a9,Build a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_9d4ea828,construct a inclined plane and drop a 1.5kg frozen orb at the top so it will slide down due to gravity,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_0225325a,drop a 0.9kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_2faa2a86,create a orb,2,collision,simple,"collision,impact,momentum",False,True,False
example_e9a66acf,Build a incline,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_b3b23aeb,form a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_c97707b5,build a orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_00fffa27,drop a 1.8kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_0395ab28,position a 1.3kg round object in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_bb71a542,position a 1.8kg orb in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_e423cdd5,insert a glass orb and a timber inclined plane,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_de5fee19,place a 1.3kg sphere on top of a slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_994eeb0a,construct a cube and a sphere next to each other,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_5337f414,set a 1.2kg round object in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_591a81d3,Build a inclined plane,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_9802c368,add a rubber ball,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_a9f686ce,introduce a wood ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_debfdfe4,drop a 0.8kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_0f1fd6ea,form a ramp and put a 1.2kg wood ball at the top so it will move down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_952be06f,build a orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_43553a2b,create a ramp and set a 1.9kg wood orb at the top so it will move down due to gravity,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_c3195a8f,drop a 0.7kg orb in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_fcc560eb,introduce a wooden round object,2,drop,simple,"drop,gravity,falling",False,True,True
example_0ee4ce79,construct a box,1,drop,simple,"drop,gravity,falling",False,False,True
example_95bec0af,drop a 4.9kg round object in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_fa141b7d,build a inclined plane and set a ball on it,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_e21a6225,create a ball,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ecf5a984,create a round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_239847f3,introduce a rubber round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_2f0cb3d7,Build a slope,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_c108df93,set a 1.9kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_ae641e64,introduce a timber orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_8e6b77b5,build a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_21798fcd,construct a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_9397e870,form a ramp and place a 2.9kg iron ball at the top so it will slide down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_39cc8fed,put a 0.4kg sphere in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_40ce1d43,position a 2.3kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_b2198e09,insert a wooden orb,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ad784fc9,construct a incline and place a sphere on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_b972d010,place a 1.1kg round object in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_33ac1aa3,Build a inclined plane,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_f8e19f4c,drop a 1.4kg orb in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_5cbbeb38,form a ramp and drop a 0.8kg polymer orb at the top so it will move down due to gravity,5,complex,complex,"complex,multi-object,interaction",True,True,False
example_a9827a3d,make a incline and place a ball on it,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_11a12326,insert a stone orb,2,collision,simple,"collision,impact,momentum",False,True,False
example_dc9aaf3c,place a 0.8kg round object in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_4526d9bd,introduce a elastic round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_38390e2f,create a orb,2,drop,simple,"drop,gravity,falling",False,True,False
example_b0f79625,include a elastic ball,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
