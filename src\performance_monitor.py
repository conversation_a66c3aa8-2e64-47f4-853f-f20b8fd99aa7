"""
Performance Monitoring Dashboard
Provides optional performance metrics display that can be toggled on/off
without disrupting the main interface.
"""

import time
import psutil
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import deque
import statistics


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    timestamp: float
    cpu_usage: float
    memory_usage: float
    gpu_usage: float = 0.0
    fps: float = 0.0
    frame_time: float = 0.0
    physics_step_time: float = 0.0
    ml_inference_time: float = 0.0
    total_objects: int = 0
    active_simulations: int = 0


@dataclass
class PerformanceStats:
    """Aggregated performance statistics."""
    avg_cpu: float = 0.0
    max_cpu: float = 0.0
    avg_memory: float = 0.0
    max_memory: float = 0.0
    avg_fps: float = 0.0
    min_fps: float = 0.0
    avg_frame_time: float = 0.0
    max_frame_time: float = 0.0
    total_frames: int = 0
    uptime: float = 0.0


class PerformanceMonitor:
    """Monitors system and application performance metrics."""
    
    def __init__(self, history_size: int = 1000, update_interval: float = 1.0):
        """Initialize performance monitor."""
        self.history_size = history_size
        self.update_interval = update_interval
        
        # Metrics storage
        self.metrics_history = deque(maxlen=history_size)
        self.current_metrics = PerformanceMetrics(timestamp=time.time(), cpu_usage=0, memory_usage=0)
        
        # Monitoring state
        self.monitoring_enabled = False
        self.monitor_thread = None
        self.start_time = time.time()
        
        # Performance tracking
        self.frame_times = deque(maxlen=60)  # Last 60 frames for FPS calculation
        self.physics_times = deque(maxlen=100)
        self.ml_times = deque(maxlen=100)
        
        # Callbacks for custom metrics
        self.custom_metric_callbacks = {}
        
        print("📊 Performance monitor initialized")
    
    def start_monitoring(self):
        """Start performance monitoring in background thread."""
        if self.monitoring_enabled:
            return
        
        self.monitoring_enabled = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        print("📊 Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        self.monitoring_enabled = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        print("📊 Performance monitoring stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop."""
        while self.monitoring_enabled:
            try:
                # Collect system metrics
                metrics = self._collect_metrics()
                
                # Store metrics
                self.current_metrics = metrics
                self.metrics_history.append(metrics)
                
                # Sleep until next update
                time.sleep(self.update_interval)
                
            except Exception as e:
                print(f"⚠️ Performance monitoring error: {e}")
                time.sleep(self.update_interval)
    
    def _collect_metrics(self) -> PerformanceMetrics:
        """Collect current performance metrics."""
        current_time = time.time()
        
        # System metrics
        cpu_usage = psutil.cpu_percent(interval=None)
        memory_info = psutil.virtual_memory()
        memory_usage = memory_info.percent
        
        # GPU metrics (if available)
        gpu_usage = self._get_gpu_usage()
        
        # Application metrics
        fps = self._calculate_fps()
        frame_time = self._calculate_avg_frame_time()
        physics_time = self._calculate_avg_physics_time()
        ml_time = self._calculate_avg_ml_time()
        
        # Custom metrics
        total_objects = self._get_custom_metric('total_objects', 0)
        active_simulations = self._get_custom_metric('active_simulations', 0)
        
        return PerformanceMetrics(
            timestamp=current_time,
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            gpu_usage=gpu_usage,
            fps=fps,
            frame_time=frame_time,
            physics_step_time=physics_time,
            ml_inference_time=ml_time,
            total_objects=total_objects,
            active_simulations=active_simulations
        )
    
    def _get_gpu_usage(self) -> float:
        """Get GPU usage if available."""
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()
            if gpus:
                return gpus[0].load * 100
        except ImportError:
            pass
        except Exception:
            pass
        return 0.0
    
    def _calculate_fps(self) -> float:
        """Calculate current FPS."""
        if len(self.frame_times) < 2:
            return 0.0
        
        # Calculate FPS from recent frame times
        recent_times = list(self.frame_times)[-30:]  # Last 30 frames
        if len(recent_times) > 1:
            avg_frame_time = statistics.mean(recent_times)
            if avg_frame_time > 0:
                return 1.0 / avg_frame_time
        
        return 0.0
    
    def _calculate_avg_frame_time(self) -> float:
        """Calculate average frame time in milliseconds."""
        if not self.frame_times:
            return 0.0
        
        recent_times = list(self.frame_times)[-10:]  # Last 10 frames
        return statistics.mean(recent_times) * 1000  # Convert to ms
    
    def _calculate_avg_physics_time(self) -> float:
        """Calculate average physics step time in milliseconds."""
        if not self.physics_times:
            return 0.0
        
        recent_times = list(self.physics_times)[-10:]
        return statistics.mean(recent_times) * 1000  # Convert to ms
    
    def _calculate_avg_ml_time(self) -> float:
        """Calculate average ML inference time in milliseconds."""
        if not self.ml_times:
            return 0.0
        
        recent_times = list(self.ml_times)[-10:]
        return statistics.mean(recent_times) * 1000  # Convert to ms
    
    def _get_custom_metric(self, metric_name: str, default: Any = 0) -> Any:
        """Get custom metric value."""
        if metric_name in self.custom_metric_callbacks:
            try:
                return self.custom_metric_callbacks[metric_name]()
            except Exception:
                return default
        return default
    
    def record_frame_time(self, frame_time: float):
        """Record frame rendering time."""
        self.frame_times.append(frame_time)
    
    def record_physics_time(self, physics_time: float):
        """Record physics step time."""
        self.physics_times.append(physics_time)
    
    def record_ml_time(self, ml_time: float):
        """Record ML inference time."""
        self.ml_times.append(ml_time)
    
    def register_custom_metric(self, metric_name: str, callback: Callable[[], Any]):
        """Register custom metric callback."""
        self.custom_metric_callbacks[metric_name] = callback
        print(f"📊 Registered custom metric: {metric_name}")
    
    def get_current_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics."""
        return self.current_metrics
    
    def get_performance_stats(self, duration_minutes: float = 5.0) -> PerformanceStats:
        """Get aggregated performance statistics."""
        cutoff_time = time.time() - (duration_minutes * 60)
        
        # Filter recent metrics
        recent_metrics = [m for m in self.metrics_history if m.timestamp > cutoff_time]
        
        if not recent_metrics:
            return PerformanceStats()
        
        # Calculate statistics
        cpu_values = [m.cpu_usage for m in recent_metrics]
        memory_values = [m.memory_usage for m in recent_metrics]
        fps_values = [m.fps for m in recent_metrics if m.fps > 0]
        frame_time_values = [m.frame_time for m in recent_metrics if m.frame_time > 0]
        
        stats = PerformanceStats(
            avg_cpu=statistics.mean(cpu_values) if cpu_values else 0,
            max_cpu=max(cpu_values) if cpu_values else 0,
            avg_memory=statistics.mean(memory_values) if memory_values else 0,
            max_memory=max(memory_values) if memory_values else 0,
            avg_fps=statistics.mean(fps_values) if fps_values else 0,
            min_fps=min(fps_values) if fps_values else 0,
            avg_frame_time=statistics.mean(frame_time_values) if frame_time_values else 0,
            max_frame_time=max(frame_time_values) if frame_time_values else 0,
            total_frames=len(self.frame_times),
            uptime=time.time() - self.start_time
        )
        
        return stats
    
    def get_metrics_history(self, duration_minutes: float = 10.0) -> List[PerformanceMetrics]:
        """Get metrics history for specified duration."""
        cutoff_time = time.time() - (duration_minutes * 60)
        return [m for m in self.metrics_history if m.timestamp > cutoff_time]
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        stats = self.get_performance_stats()
        current = self.get_current_metrics()
        
        # System information
        system_info = {
            'cpu_count': psutil.cpu_count(),
            'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
            'python_version': f"{psutil.version_info}",
        }
        
        # Performance summary
        performance_summary = {
            'uptime_hours': round(stats.uptime / 3600, 2),
            'avg_cpu_usage': round(stats.avg_cpu, 1),
            'avg_memory_usage': round(stats.avg_memory, 1),
            'avg_fps': round(stats.avg_fps, 1),
            'avg_frame_time_ms': round(stats.avg_frame_time, 2),
            'total_frames_rendered': stats.total_frames
        }
        
        # Current status
        current_status = {
            'cpu_usage': round(current.cpu_usage, 1),
            'memory_usage': round(current.memory_usage, 1),
            'fps': round(current.fps, 1),
            'frame_time_ms': round(current.frame_time, 2),
            'physics_time_ms': round(current.physics_step_time, 2),
            'ml_time_ms': round(current.ml_inference_time, 2),
            'total_objects': current.total_objects,
            'active_simulations': current.active_simulations
        }
        
        # Performance warnings
        warnings = []
        if stats.avg_cpu > 80:
            warnings.append("High CPU usage detected")
        if stats.avg_memory > 85:
            warnings.append("High memory usage detected")
        if stats.avg_fps < 30 and stats.avg_fps > 0:
            warnings.append("Low FPS detected")
        if stats.avg_frame_time > 33:  # > 33ms = < 30 FPS
            warnings.append("High frame time detected")
        
        return {
            'timestamp': time.time(),
            'system_info': system_info,
            'performance_summary': performance_summary,
            'current_status': current_status,
            'warnings': warnings,
            'metrics_collected': len(self.metrics_history)
        }
    
    def export_metrics(self, filepath: str, format: str = "json"):
        """Export metrics data to file."""
        import json
        import csv
        
        if format == "json":
            data = {
                'export_timestamp': time.time(),
                'metrics': [
                    {
                        'timestamp': m.timestamp,
                        'cpu_usage': m.cpu_usage,
                        'memory_usage': m.memory_usage,
                        'fps': m.fps,
                        'frame_time': m.frame_time,
                        'physics_time': m.physics_step_time,
                        'ml_time': m.ml_inference_time,
                        'total_objects': m.total_objects
                    }
                    for m in self.metrics_history
                ]
            }
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
        
        elif format == "csv":
            with open(filepath, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'cpu_usage', 'memory_usage', 'fps',
                    'frame_time', 'physics_time', 'ml_time', 'total_objects'
                ])
                
                for m in self.metrics_history:
                    writer.writerow([
                        m.timestamp, m.cpu_usage, m.memory_usage, m.fps,
                        m.frame_time, m.physics_step_time, m.ml_inference_time, m.total_objects
                    ])
        
        print(f"📊 Metrics exported to: {filepath}")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get data formatted for dashboard display."""
        current = self.get_current_metrics()
        stats = self.get_performance_stats(duration_minutes=1.0)  # Last minute
        
        return {
            'current': {
                'cpu': f"{current.cpu_usage:.1f}%",
                'memory': f"{current.memory_usage:.1f}%",
                'fps': f"{current.fps:.1f}",
                'frame_time': f"{current.frame_time:.1f}ms",
                'physics_time': f"{current.physics_step_time:.1f}ms",
                'ml_time': f"{current.ml_inference_time:.1f}ms",
                'objects': current.total_objects,
                'simulations': current.active_simulations
            },
            'averages': {
                'cpu': f"{stats.avg_cpu:.1f}%",
                'memory': f"{stats.avg_memory:.1f}%",
                'fps': f"{stats.avg_fps:.1f}",
                'frame_time': f"{stats.avg_frame_time:.1f}ms"
            },
            'uptime': f"{stats.uptime/3600:.1f}h",
            'status': self._get_performance_status(current, stats)
        }
    
    def _get_performance_status(self, current: PerformanceMetrics, stats: PerformanceStats) -> str:
        """Get overall performance status."""
        if current.cpu_usage > 90 or current.memory_usage > 90:
            return "Critical"
        elif current.cpu_usage > 70 or current.memory_usage > 70:
            return "Warning"
        elif stats.avg_fps > 0 and stats.avg_fps < 20:
            return "Poor"
        else:
            return "Good"


def test_performance_monitor():
    """Test the performance monitoring system."""
    print("Testing Performance Monitor...")
    
    # Create monitor
    monitor = PerformanceMonitor(update_interval=0.1)
    
    # Test custom metrics
    def get_test_objects():
        return 5
    
    def get_test_simulations():
        return 2
    
    monitor.register_custom_metric('total_objects', get_test_objects)
    monitor.register_custom_metric('active_simulations', get_test_simulations)
    
    # Start monitoring
    monitor.start_monitoring()
    
    # Simulate some activity
    for i in range(10):
        monitor.record_frame_time(0.016)  # ~60 FPS
        monitor.record_physics_time(0.005)  # 5ms physics
        monitor.record_ml_time(0.010)  # 10ms ML
        time.sleep(0.1)
    
    # Get metrics
    current = monitor.get_current_metrics()
    stats = monitor.get_performance_stats()
    report = monitor.generate_performance_report()
    dashboard_data = monitor.get_dashboard_data()
    
    print(f"✅ Current CPU: {current.cpu_usage:.1f}%")
    print(f"✅ Current Memory: {current.memory_usage:.1f}%")
    print(f"✅ Current FPS: {current.fps:.1f}")
    print(f"✅ Average CPU: {stats.avg_cpu:.1f}%")
    print(f"✅ Dashboard status: {dashboard_data['status']}")
    print(f"✅ Performance warnings: {len(report['warnings'])}")
    
    # Stop monitoring
    monitor.stop_monitoring()
    
    print("✅ Performance monitor test completed!")


if __name__ == "__main__":
    test_performance_monitor()
