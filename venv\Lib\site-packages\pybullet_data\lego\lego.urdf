<?xml version="0.0" ?>
<robot name="cube.urdf">
  <link name="legobrick">
    <contact>
      <lateral_friction value="1.0"/>
      <rolling_friction value="0.0"/>
      <contact_cfm value="0.0"/>
      <contact_erp value="1.0"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0 0.0 0.0"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="1.570796 0 0" xyz="-0.016 -0.016 -0.0115"/>
      <geometry>
        <mesh filename="lego.obj" scale=".1 .1 .1"/>
      </geometry>
       <material name="yellow">
        <color rgba="1 1 0.4 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="1.570796 0 0" xyz="0 0 0"/>
      <geometry>
	 			<box size="0.032 0.023 0.032"/>
      </geometry>
    </collision>
  </link>
</robot>

