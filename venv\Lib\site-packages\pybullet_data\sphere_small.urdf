<?xml version="0.0" ?>
<robot name="urdf_robot">
  <link name="base_link">
    <contact>
      <rolling_friction value="0.001"/>
      <spinning_friction value="0.001"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value=".1"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
				<mesh filename="textured_sphere_smooth.obj" scale="0.03 0.03 0.03"/>      
      </geometry>
      <material name="white">
        <color rgba="1 1 1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
	 <sphere radius="0.03"/>
      </geometry>
    </collision>
  </link>
</robot>

