<?xml version="1.0" ?>
<robot name="cube">
  <link name="baseLink">
    <contact>
      <lateral_friction value="0.5"/>
    </contact>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value="0.025"/>
       <inertia ixx="1" ixy="0" ixz="0" iyy="1" iyz="0" izz="1"/>
    </inertial>
    <visual>
      <origin rpy="0 0 1.57" xyz="0 0 0"/>
      <geometry>
				<mesh filename="domino.obj" scale="1 1 1"/>
      </geometry>
       <material name="white">
        <color rgba="1 1 1 1"/>
      </material>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
	 	<box size="0.00635 0.0254 0.0508"/>
      </geometry>
    </collision>
  </link>
</robot>

