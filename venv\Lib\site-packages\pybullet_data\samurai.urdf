<?xml version="0.0" ?>
<robot name="cube.urdf">
  <link concave="yes" name="baseLink">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0"/>
       <mass value=".0"/>
       <inertia ixx="0" ixy="0" ixz="0" iyy="0" iyz="0" izz="0"/>
    </inertial>
    <visual>
      <origin rpy="1.5707963 0 0" xyz="0 0 0"/>
      <geometry>
				<mesh filename="samurai_monastry.obj" scale="1 1 1"/>
      </geometry>
       <material name="white">
        <color rgba="1 1 1 1"/>
      </material>
    </visual>
    <collision concave="yes">
      <origin rpy="1.5707963 0 0" xyz="0 0 0"/>
     <geometry>
				<mesh filename="samurai_monastry.obj" scale="1 1 1"/>
      </geometry>
    </collision>
  </link>
</robot>

