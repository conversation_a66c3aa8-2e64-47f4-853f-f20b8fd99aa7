<?xml version="1.0" ?>
<!-- ======================================================================= -->
<!-- | This document was autogenerated by xacro from lbr_iiwa.urdf.xacro   | -->
<!-- | Original xacro: https://github.com/rtkg/lbr_iiwa/archive/master.zip | -->
<!-- | EDITING THIS FILE BY HAND IS NOT RECOMMENDED                        | -->
<!-- | Changes (kohlhoff):                                                 | -->
<!-- |   * Removed gazebo tags.                                            | -->
<!-- |   * Removed unused materials.                                       | -->
<!-- |   * Made mesh paths relative.                                       | -->
<!-- |   * Removed material fields from collision segments.                | -->
<!-- |   * Removed the self_collision_checking segment.                    | -->
<!-- |   * Removed transmission segments, since they didn't match the      | -->
<!-- |     convention, will have to added back later.                      | -->
<!-- ======================================================================= -->
<!--LICENSE:                                                                 -->
<!--Copyright (c) 2015, Robert Krug & Todor Stoyanov, AASS Research Center,  -->
<!--Orebro University, Sweden                                                -->
<!--All rights reserved.                                                     -->
<!--                                                                         -->
<!--Redistribution and use in source and binary forms, with or without       -->
<!--modification, are permitted provided that the following conditions are   -->
<!--met:                                                                     -->
<!--                                                                         -->
<!--1. Redistributions of source code must retain the above copyright notice,-->
<!--   this list of conditions and the following disclaimer.                 -->
<!--                                                                         -->
<!--2. Redistributions in binary form must reproduce the above copyright     -->
<!--   notice, this list of conditions and the following disclaimer in the   -->
<!--   documentation and/or other materials provided with the distribution.  -->
<!--                                                                         -->
<!--THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS  -->
<!--IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,-->
<!--THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR   -->
<!--PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR        -->
<!--CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,    -->
<!--EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,      -->
<!--PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR       -->
<!--PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF   -->
<!--LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING     -->
<!--NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS       -->
<!--SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.             -->
<robot name="lbr_iiwa" xmlns:xacro="http://www.ros.org/wiki/xacro">
  <!-- Import Rviz colors -->
  <material name="Grey">
    <color rgba="0.2 0.2 0.2 1.0"/>
  </material>
  <material name="Orange">
    <color rgba="1.0 0.423529411765 0.0392156862745 1.0"/>
  </material>
  <material name="Blue">
  <color rgba="0.5 0.7 1.0 1.0"/>      
</material>

  <!--Import the lbr iiwa macro -->
  <!--Import Transmissions -->
  <!--Include Utilities -->
  <!--The following macros are adapted from the LWR 4 definitions of the RCPRG - https://github.com/RCPRG-ros-pkg/lwr_robot -->
  <!--Little helper macros to define the inertia matrix needed for links.-->
  <!--Cuboid-->
  <!--Cylinder: length is along the y-axis! -->
  <!--lbr-->
  <link name="lbr_iiwa_link_0">
    <inertial>
      <origin rpy="0 0 0" xyz="-0.1 0 0.07"/>
      <!--Increase mass from 5 Kg original to provide a stable base to carry the
          arm.-->
      <mass value="0.0"/>
      <inertia ixx="0.05" ixy="0" ixz="0" iyy="0.06" iyz="0" izz="0.03"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_0.stl"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_0.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- joint between link_0 and link_1 -->
  <joint name="lbr_iiwa_joint_1" type="revolute">
    <parent link="lbr_iiwa_link_0"/>
    <child link="lbr_iiwa_link_1"/>
    <origin rpy="0 0 0" xyz="0 0 0.1575"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-.96705972839" upper="0.96705972839" velocity="10"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="lbr_iiwa_link_1">
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.03 0.12"/>
      <mass value="4"/>
      <inertia ixx="0.1" ixy="0" ixz="0" iyy="0.09" iyz="0" izz="0.02"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_1.stl"/>
      </geometry>
      <material name="Blue"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_1.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- joint between link_1 and link_2 -->
  <joint name="lbr_iiwa_joint_2" type="revolute">
    <parent link="lbr_iiwa_link_1"/>
    <child link="lbr_iiwa_link_2"/>
    <origin rpy="1.57079632679   0 3.14159265359" xyz="0 0 0.2025"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.09439510239" upper="2.09439510239" velocity="10"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="lbr_iiwa_link_2">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0003 0.059 0.042"/>
      <mass value="4"/>
      <inertia ixx="0.05" ixy="0" ixz="0" iyy="0.018" iyz="0" izz="0.044"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_2.stl"/>
      </geometry>
      <material name="Blue"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_2.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- joint between link_2 and link_3 -->
  <joint name="lbr_iiwa_joint_3" type="revolute">
    <parent link="lbr_iiwa_link_2"/>
    <child link="lbr_iiwa_link_3"/>
    <origin rpy="1.57079632679 0 3.14159265359" xyz="0 0.2045 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.96705972839" upper="2.96705972839" velocity="10"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="lbr_iiwa_link_3">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.03 0.13"/>
      <mass value="3"/>
      <inertia ixx="0.08" ixy="0" ixz="0" iyy="0.075" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_3.stl"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_3.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- joint between link_3 and link_4 -->
  <joint name="lbr_iiwa_joint_4" type="revolute">
    <parent link="lbr_iiwa_link_3"/>
    <child link="lbr_iiwa_link_4"/>
    <origin rpy="1.57079632679 0 0" xyz="0 0 0.2155"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="0.19439510239" upper="2.29439510239" velocity="10"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="lbr_iiwa_link_4">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.067 0.034"/>
      <mass value="2.7"/>
      <inertia ixx="0.03" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.029"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_4.stl"/>
      </geometry>
      <material name="Blue"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_4.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- joint between link_4 and link_5 -->
  <joint name="lbr_iiwa_joint_5" type="revolute">
    <parent link="lbr_iiwa_link_4"/>
    <child link="lbr_iiwa_link_5"/>
    <origin rpy="-1.57079632679 3.14159265359 0" xyz="0 0.1845 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.96705972839" upper="2.96705972839" velocity="10"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="lbr_iiwa_link_5">
    <inertial>
      <origin rpy="0 0 0" xyz="0.0001 0.021 0.076"/>
      <mass value="1.7"/>
      <inertia ixx="0.02" ixy="0" ixz="0" iyy="0.018" iyz="0" izz="0.005"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_5.stl"/>
      </geometry>
      <material name="Blue"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_5.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- joint between link_5 and link_6 -->
  <joint name="lbr_iiwa_joint_6" type="revolute">
    <parent link="lbr_iiwa_link_5"/>
    <child link="lbr_iiwa_link_6"/>
    <origin rpy="1.57079632679 0 0" xyz="0 0 0.2155"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-2.09439510239" upper="2.09439510239" velocity="10"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="lbr_iiwa_link_6">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.0006 0.0004"/>
      <mass value="1.8"/>
      <inertia ixx="0.005" ixy="0" ixz="0" iyy="0.0036" iyz="0" izz="0.0047"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_6.stl"/>
      </geometry>
      <material name="Orange"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_6.stl"/>
      </geometry>
    </collision>
  </link>
  <!-- joint between link_6 and link_7 -->
  <joint name="lbr_iiwa_joint_7" type="revolute">
    <parent link="lbr_iiwa_link_6"/>
    <child link="lbr_iiwa_link_7"/>
    <origin rpy="-1.57079632679 3.14159265359 0" xyz="0 0.081 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="300" lower="-3.05432619099" upper="3.05432619099" velocity="10"/>
    <dynamics damping="0.5"/>
  </joint>
  <link name="lbr_iiwa_link_7">
    <inertial>
      <origin rpy="0 0 0" xyz="0 0 0.02"/>
      <mass value="0.3"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_7.stl"/>
      </geometry>
      <material name="Grey"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="meshes/link_7.stl"/>
      </geometry>
    </collision>
  </link>
  
</robot>

