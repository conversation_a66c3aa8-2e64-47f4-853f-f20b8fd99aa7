"""
Keyboard Shortcuts and Hotkeys
Implements keyboard shortcuts for common actions to enhance productivity
without changing the visual interface.
"""

import tkinter as tk
from typing import Dict, List, Callable, Optional, Any
from dataclasses import dataclass
from enum import Enum


class ShortcutCategory(Enum):
    """Categories of keyboard shortcuts."""
    GENERAL = "general"
    SIMULATION = "simulation"
    OBJECTS = "objects"
    VIEW = "view"
    FILE = "file"
    DEBUG = "debug"


@dataclass
class KeyboardShortcut:
    """Represents a keyboard shortcut."""
    key_combination: str
    description: str
    callback: Callable
    category: ShortcutCategory
    enabled: bool = True
    
    def __str__(self):
        return f"{self.key_combination}: {self.description}"


class KeyboardShortcutManager:
    """Manages keyboard shortcuts for the application."""
    
    def __init__(self, root_widget: tk.Widget):
        """Initialize keyboard shortcut manager."""
        self.root = root_widget
        self.shortcuts = {}  # key_combination -> KeyboardShortcut
        self.enabled = True
        
        # Modifier key mappings for cross-platform compatibility
        self.modifier_map = {
            'ctrl': '<Control>',
            'alt': '<Alt>',
            'shift': '<Shift>',
            'cmd': '<Command>',  # macOS
        }
        
        print("⌨️ Keyboard shortcut manager initialized")
    
    def register_shortcut(self, key_combination: str, description: str, 
                         callback: Callable, category: ShortcutCategory = ShortcutCategory.GENERAL):
        """Register a new keyboard shortcut."""
        # Normalize key combination
        normalized_key = self._normalize_key_combination(key_combination)
        
        # Create shortcut
        shortcut = KeyboardShortcut(
            key_combination=normalized_key,
            description=description,
            callback=callback,
            category=category
        )
        
        # Store shortcut
        self.shortcuts[normalized_key] = shortcut
        
        # Bind to widget
        self._bind_shortcut(normalized_key, callback)
        
        print(f"⌨️ Registered shortcut: {normalized_key} - {description}")
    
    def _normalize_key_combination(self, key_combination: str) -> str:
        """Normalize key combination string."""
        # Convert common formats to Tkinter format
        key_combination = key_combination.lower()
        
        # Handle common shortcuts
        replacements = {
            'ctrl+': '<Control-',
            'alt+': '<Alt-',
            'shift+': '<Shift-',
            'cmd+': '<Command-',
            'enter': 'Return',
            'esc': 'Escape',
            'space': 'space',
            'tab': 'Tab',
            'del': 'Delete',
            'backspace': 'BackSpace'
        }
        
        for old, new in replacements.items():
            key_combination = key_combination.replace(old, new)
        
        # Ensure proper format
        if not key_combination.startswith('<') and not key_combination.endswith('>'):
            if '-' in key_combination:
                key_combination = f"<{key_combination}>"
            else:
                key_combination = f"<Key-{key_combination}>"
        elif key_combination.startswith('<') and not key_combination.endswith('>'):
            key_combination += '>'
        
        return key_combination
    
    def _bind_shortcut(self, key_combination: str, callback: Callable):
        """Bind shortcut to root widget."""
        def wrapper(event):
            if self.enabled and key_combination in self.shortcuts:
                shortcut = self.shortcuts[key_combination]
                if shortcut.enabled:
                    try:
                        shortcut.callback()
                    except Exception as e:
                        print(f"❌ Shortcut error ({key_combination}): {e}")
        
        self.root.bind_all(key_combination, wrapper)
    
    def unregister_shortcut(self, key_combination: str):
        """Unregister a keyboard shortcut."""
        normalized_key = self._normalize_key_combination(key_combination)
        
        if normalized_key in self.shortcuts:
            # Unbind from widget
            self.root.unbind_all(normalized_key)
            
            # Remove from shortcuts
            del self.shortcuts[normalized_key]
            
            print(f"⌨️ Unregistered shortcut: {normalized_key}")
    
    def enable_shortcut(self, key_combination: str):
        """Enable a specific shortcut."""
        normalized_key = self._normalize_key_combination(key_combination)
        
        if normalized_key in self.shortcuts:
            self.shortcuts[normalized_key].enabled = True
            print(f"⌨️ Enabled shortcut: {normalized_key}")
    
    def disable_shortcut(self, key_combination: str):
        """Disable a specific shortcut."""
        normalized_key = self._normalize_key_combination(key_combination)
        
        if normalized_key in self.shortcuts:
            self.shortcuts[normalized_key].enabled = False
            print(f"⌨️ Disabled shortcut: {normalized_key}")
    
    def enable_all_shortcuts(self):
        """Enable all keyboard shortcuts."""
        self.enabled = True
        print("⌨️ All shortcuts enabled")
    
    def disable_all_shortcuts(self):
        """Disable all keyboard shortcuts."""
        self.enabled = False
        print("⌨️ All shortcuts disabled")
    
    def get_shortcuts_by_category(self, category: ShortcutCategory) -> List[KeyboardShortcut]:
        """Get all shortcuts in a specific category."""
        return [shortcut for shortcut in self.shortcuts.values() 
                if shortcut.category == category]
    
    def get_all_shortcuts(self) -> Dict[str, KeyboardShortcut]:
        """Get all registered shortcuts."""
        return self.shortcuts.copy()
    
    def generate_help_text(self) -> List[str]:
        """Generate help text for all shortcuts."""
        help_lines = ["⌨️ Keyboard Shortcuts:", ""]
        
        # Group by category
        for category in ShortcutCategory:
            category_shortcuts = self.get_shortcuts_by_category(category)
            if category_shortcuts:
                help_lines.append(f"📁 {category.value.title()}:")
                
                for shortcut in sorted(category_shortcuts, key=lambda s: s.key_combination):
                    status = "✅" if shortcut.enabled else "❌"
                    help_lines.append(f"  {status} {shortcut.key_combination}: {shortcut.description}")
                
                help_lines.append("")
        
        return help_lines
    
    def show_help_dialog(self):
        """Show help dialog with all shortcuts."""
        help_text = "\n".join(self.generate_help_text())
        
        # Create help window
        help_window = tk.Toplevel(self.root)
        help_window.title("Keyboard Shortcuts Help")
        help_window.geometry("600x500")
        help_window.resizable(True, True)
        
        # Add text widget with scrollbar
        frame = tk.Frame(help_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_widget = tk.Text(frame, wrap=tk.WORD, font=('Consolas', 10))
        scrollbar = tk.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Insert help text
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)
        
        # Close button
        close_btn = tk.Button(help_window, text="Close", command=help_window.destroy)
        close_btn.pack(pady=5)
        
        # Center window
        help_window.transient(self.root)
        help_window.grab_set()


class DefaultShortcuts:
    """Default keyboard shortcuts for the physics application."""
    
    @staticmethod
    def setup_default_shortcuts(shortcut_manager: KeyboardShortcutManager, app_callbacks: Dict[str, Callable]):
        """Setup default keyboard shortcuts."""
        
        # General shortcuts
        if 'execute_command' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+enter', 'Execute current command', 
                app_callbacks['execute_command'], ShortcutCategory.GENERAL
            )
        
        if 'clear_scene' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+shift+c', 'Clear scene', 
                app_callbacks['clear_scene'], ShortcutCategory.GENERAL
            )
        
        if 'show_help' in app_callbacks:
            shortcut_manager.register_shortcut(
                'f1', 'Show help', 
                app_callbacks['show_help'], ShortcutCategory.GENERAL
            )
        
        # File operations
        if 'save_session' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+s', 'Save session', 
                app_callbacks['save_session'], ShortcutCategory.FILE
            )
        
        if 'load_session' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+o', 'Load session', 
                app_callbacks['load_session'], ShortcutCategory.FILE
            )
        
        if 'export_scene' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+e', 'Export scene', 
                app_callbacks['export_scene'], ShortcutCategory.FILE
            )
        
        # Simulation shortcuts
        if 'start_simulation' in app_callbacks:
            shortcut_manager.register_shortcut(
                'space', 'Start/Stop simulation', 
                app_callbacks['start_simulation'], ShortcutCategory.SIMULATION
            )
        
        if 'reset_simulation' in app_callbacks:
            shortcut_manager.register_shortcut(
                'r', 'Reset simulation', 
                app_callbacks['reset_simulation'], ShortcutCategory.SIMULATION
            )
        
        if 'step_simulation' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+space', 'Step simulation', 
                app_callbacks['step_simulation'], ShortcutCategory.SIMULATION
            )
        
        # Object creation shortcuts
        if 'create_ball' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+1', 'Create ball', 
                app_callbacks['create_ball'], ShortcutCategory.OBJECTS
            )
        
        if 'create_box' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+2', 'Create box', 
                app_callbacks['create_box'], ShortcutCategory.OBJECTS
            )
        
        if 'create_ramp' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+3', 'Create ramp', 
                app_callbacks['create_ramp'], ShortcutCategory.OBJECTS
            )
        
        # View shortcuts
        if 'reset_camera' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+0', 'Reset camera', 
                app_callbacks['reset_camera'], ShortcutCategory.VIEW
            )
        
        if 'toggle_wireframe' in app_callbacks:
            shortcut_manager.register_shortcut(
                'w', 'Toggle wireframe', 
                app_callbacks['toggle_wireframe'], ShortcutCategory.VIEW
            )
        
        if 'toggle_debug' in app_callbacks:
            shortcut_manager.register_shortcut(
                'ctrl+d', 'Toggle debug mode', 
                app_callbacks['toggle_debug'], ShortcutCategory.DEBUG
            )
        
        # Quick commands
        if 'quick_command_1' in app_callbacks:
            shortcut_manager.register_shortcut(
                'f2', 'Quick command: Create ball', 
                app_callbacks['quick_command_1'], ShortcutCategory.GENERAL
            )
        
        if 'quick_command_2' in app_callbacks:
            shortcut_manager.register_shortcut(
                'f3', 'Quick command: Ball on ramp', 
                app_callbacks['quick_command_2'], ShortcutCategory.GENERAL
            )
        
        if 'quick_command_3' in app_callbacks:
            shortcut_manager.register_shortcut(
                'f4', 'Quick command: Multiple objects', 
                app_callbacks['quick_command_3'], ShortcutCategory.GENERAL
            )
        
        print("⌨️ Default shortcuts configured")


def create_test_callbacks() -> Dict[str, Callable]:
    """Create test callbacks for demonstration."""
    callbacks = {}
    
    def make_test_callback(name: str):
        def callback():
            print(f"🎯 Executed: {name}")
        return callback
    
    # Create test callbacks
    callback_names = [
        'execute_command', 'clear_scene', 'show_help', 'save_session', 'load_session',
        'export_scene', 'start_simulation', 'reset_simulation', 'step_simulation',
        'create_ball', 'create_box', 'create_ramp', 'reset_camera', 'toggle_wireframe',
        'toggle_debug', 'quick_command_1', 'quick_command_2', 'quick_command_3'
    ]
    
    for name in callback_names:
        callbacks[name] = make_test_callback(name)
    
    return callbacks


def test_keyboard_shortcuts():
    """Test the keyboard shortcuts system."""
    print("Testing Keyboard Shortcuts System...")
    
    # Create test Tkinter window
    root = tk.Tk()
    root.title("Keyboard Shortcuts Test")
    root.geometry("400x300")
    
    # Create shortcut manager
    shortcut_manager = KeyboardShortcutManager(root)
    
    # Create test callbacks
    callbacks = create_test_callbacks()
    
    # Setup default shortcuts
    DefaultShortcuts.setup_default_shortcuts(shortcut_manager, callbacks)
    
    # Test individual shortcut registration
    shortcut_manager.register_shortcut(
        'ctrl+t', 'Test shortcut', 
        lambda: print("🧪 Test shortcut activated!"), 
        ShortcutCategory.DEBUG
    )
    
    # Display information
    info_label = tk.Label(
        root, 
        text="Keyboard Shortcuts Test\n\nTry pressing:\n• Ctrl+Enter\n• F1 (Help)\n• Space\n• Ctrl+T\n\nPress F1 for full list",
        justify=tk.LEFT,
        font=('Arial', 10)
    )
    info_label.pack(expand=True)
    
    # Add help button
    help_btn = tk.Button(
        root, 
        text="Show Shortcuts Help (F1)", 
        command=shortcut_manager.show_help_dialog
    )
    help_btn.pack(pady=10)
    
    # Test statistics
    all_shortcuts = shortcut_manager.get_all_shortcuts()
    print(f"✅ Registered {len(all_shortcuts)} shortcuts")
    
    for category in ShortcutCategory:
        category_shortcuts = shortcut_manager.get_shortcuts_by_category(category)
        print(f"✅ {category.value}: {len(category_shortcuts)} shortcuts")
    
    print("✅ Keyboard shortcuts test setup complete!")
    print("🎯 Try the shortcuts in the test window")
    
    # Don't run mainloop in test - just verify setup
    root.destroy()
    
    print("✅ Keyboard shortcuts test completed!")


if __name__ == "__main__":
    test_keyboard_shortcuts()
