"""
Week 9: UI Polish & Advanced Features Demo
Demonstrates the complete Week 9 enhancements:
- Advanced visualization options
- Export/save functionality  
- Performance monitoring
- Keyboard shortcuts
- Enhanced error handling
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

import time
import tkinter as tk
from typing import Dict, List, Any

from advanced_visualization import AdvancedVisualization, VisualizationMode
from export_save_system import ExportSaveSystem, ExportSettings
from performance_monitor import PerformanceMonitor
from keyboard_shortcuts import KeyboardShortcutManager, DefaultShortcuts
from enhanced_error_handling import EnhancedErrorHandler, FeedbackType
from dynamic_scene_representation import DynamicPhysicsScene, DynamicPhysicsObject
from scene_representation import ObjectType, MaterialType, Vector3


class Week9UIPolishDemo:
    """Demonstrates the complete Week 9 UI polish and advanced features."""
    
    def __init__(self):
        """Initialize the demo system."""
        print("🎨 Initializing Week 9 UI Polish & Advanced Features Demo...")
        
        # Create demo window
        self.root = tk.Tk()
        self.root.title("Week 9: UI Polish & Advanced Features Demo")
        self.root.geometry("800x600")
        
        # Initialize all Week 9 components
        self.visualization = AdvancedVisualization()
        self.export_system = ExportSaveSystem("demo_saves")
        self.performance_monitor = PerformanceMonitor()
        self.shortcut_manager = KeyboardShortcutManager(self.root)
        self.error_handler = EnhancedErrorHandler(self.root)
        
        # Demo state
        self.demo_scene = DynamicPhysicsScene("week9_demo")
        self.demo_running = False
        
        # Setup UI
        self.setup_demo_ui()
        self.setup_keyboard_shortcuts()
        
        print("✅ Week 9 demo system initialized!")
    
    def setup_demo_ui(self):
        """Setup the demo user interface."""
        # Main title
        title_label = tk.Label(
            self.root,
            text="Week 9: UI Polish & Advanced Features Demo",
            font=('Arial', 16, 'bold'),
            bg='lightblue',
            pady=10
        )
        title_label.pack(fill=tk.X)
        
        # Create notebook for different feature categories
        notebook = tk.Frame(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Visualization features
        viz_frame = tk.LabelFrame(notebook, text="🎨 Advanced Visualization", font=('Arial', 12, 'bold'))
        viz_frame.pack(fill=tk.X, pady=5)
        
        viz_buttons = tk.Frame(viz_frame)
        viz_buttons.pack(pady=5)
        
        tk.Button(viz_buttons, text="Standard View", command=self.set_standard_view).pack(side=tk.LEFT, padx=2)
        tk.Button(viz_buttons, text="Physics Debug", command=self.set_debug_view).pack(side=tk.LEFT, padx=2)
        tk.Button(viz_buttons, text="Trajectory Trace", command=self.set_trajectory_view).pack(side=tk.LEFT, padx=2)
        tk.Button(viz_buttons, text="Force Vectors", command=self.set_force_view).pack(side=tk.LEFT, padx=2)
        tk.Button(viz_buttons, text="Reset Camera", command=self.reset_camera).pack(side=tk.LEFT, padx=2)
        
        # Export/Save features
        export_frame = tk.LabelFrame(notebook, text="💾 Export/Save System", font=('Arial', 12, 'bold'))
        export_frame.pack(fill=tk.X, pady=5)
        
        export_buttons = tk.Frame(export_frame)
        export_buttons.pack(pady=5)
        
        tk.Button(export_buttons, text="Save Session", command=self.save_session).pack(side=tk.LEFT, padx=2)
        tk.Button(export_buttons, text="Load Session", command=self.load_session).pack(side=tk.LEFT, padx=2)
        tk.Button(export_buttons, text="Export Scene", command=self.export_scene).pack(side=tk.LEFT, padx=2)
        tk.Button(export_buttons, text="List Sessions", command=self.list_sessions).pack(side=tk.LEFT, padx=2)
        
        # Performance monitoring
        perf_frame = tk.LabelFrame(notebook, text="📊 Performance Monitor", font=('Arial', 12, 'bold'))
        perf_frame.pack(fill=tk.X, pady=5)
        
        perf_buttons = tk.Frame(perf_frame)
        perf_buttons.pack(pady=5)
        
        tk.Button(perf_buttons, text="Start Monitoring", command=self.start_monitoring).pack(side=tk.LEFT, padx=2)
        tk.Button(perf_buttons, text="Stop Monitoring", command=self.stop_monitoring).pack(side=tk.LEFT, padx=2)
        tk.Button(perf_buttons, text="Show Stats", command=self.show_performance_stats).pack(side=tk.LEFT, padx=2)
        tk.Button(perf_buttons, text="Generate Report", command=self.generate_performance_report).pack(side=tk.LEFT, padx=2)
        
        # Error handling demo
        error_frame = tk.LabelFrame(notebook, text="🛡️ Enhanced Error Handling", font=('Arial', 12, 'bold'))
        error_frame.pack(fill=tk.X, pady=5)
        
        error_buttons = tk.Frame(error_frame)
        error_buttons.pack(pady=5)
        
        tk.Button(error_buttons, text="Test Success", command=self.test_success_message).pack(side=tk.LEFT, padx=2)
        tk.Button(error_buttons, text="Test Warning", command=self.test_warning).pack(side=tk.LEFT, padx=2)
        tk.Button(error_buttons, text="Test Error", command=self.test_error).pack(side=tk.LEFT, padx=2)
        tk.Button(error_buttons, text="Test Loading", command=self.test_loading).pack(side=tk.LEFT, padx=2)
        tk.Button(error_buttons, text="Test Progress", command=self.test_progress).pack(side=tk.LEFT, padx=2)
        
        # Keyboard shortcuts info
        shortcuts_frame = tk.LabelFrame(notebook, text="⌨️ Keyboard Shortcuts", font=('Arial', 12, 'bold'))
        shortcuts_frame.pack(fill=tk.X, pady=5)
        
        shortcuts_info = tk.Label(
            shortcuts_frame,
            text="Press F1 for help • Ctrl+S to save • Space for simulation • Ctrl+D for debug",
            font=('Arial', 9),
            fg='blue'
        )
        shortcuts_info.pack(pady=5)
        
        # Status bar
        self.status_bar = tk.Label(
            self.root,
            text="Ready - Press F1 for keyboard shortcuts help",
            relief=tk.SUNKEN,
            anchor=tk.W,
            font=('Arial', 9)
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Demo content area
        content_frame = tk.Frame(notebook, bg='white', relief=tk.SUNKEN, bd=2)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.content_label = tk.Label(
            content_frame,
            text="Week 9 Advanced Features Demo\n\nThis demo showcases all the UI polish and advanced features:\n\n"
                 "🎨 Advanced Visualization - Multiple view modes and camera controls\n"
                 "💾 Export/Save System - Session management and data export\n"
                 "📊 Performance Monitor - Real-time performance tracking\n"
                 "⌨️ Keyboard Shortcuts - Productivity enhancements\n"
                 "🛡️ Enhanced Error Handling - User-friendly feedback\n\n"
                 "Try the buttons above or use keyboard shortcuts!",
            bg='white',
            font=('Arial', 11),
            justify=tk.LEFT
        )
        self.content_label.pack(expand=True, pady=20)
    
    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for the demo."""
        # Create demo callbacks
        callbacks = {
            'show_help': self.show_shortcuts_help,
            'save_session': self.save_session,
            'load_session': self.load_session,
            'export_scene': self.export_scene,
            'start_simulation': self.toggle_demo,
            'reset_simulation': self.reset_demo,
            'toggle_debug': self.toggle_debug_mode,
            'create_ball': self.create_demo_ball,
            'create_box': self.create_demo_box,
            'reset_camera': self.reset_camera
        }
        
        # Setup default shortcuts
        DefaultShortcuts.setup_default_shortcuts(self.shortcut_manager, callbacks)
        
        # Add demo-specific shortcuts
        self.shortcut_manager.register_shortcut(
            'ctrl+shift+d', 'Toggle demo mode',
            self.toggle_demo
        )
    
    def update_status(self, message: str):
        """Update status bar message."""
        self.status_bar.config(text=message)
        self.root.update_idletasks()
    
    # Visualization methods
    def set_standard_view(self):
        """Set standard visualization mode."""
        self.visualization.set_visualization_mode(VisualizationMode.STANDARD)
        self.update_status("Visualization: Standard view mode")
        self.error_handler.show_info_message("Visualization", "Switched to standard view mode")
    
    def set_debug_view(self):
        """Set debug visualization mode."""
        self.visualization.set_visualization_mode(VisualizationMode.PHYSICS_DEBUG)
        self.update_status("Visualization: Physics debug mode")
        self.error_handler.show_info_message("Visualization", "Switched to physics debug mode")
    
    def set_trajectory_view(self):
        """Set trajectory visualization mode."""
        self.visualization.set_visualization_mode(VisualizationMode.TRAJECTORY_TRACE)
        self.update_status("Visualization: Trajectory trace mode")
        self.error_handler.show_info_message("Visualization", "Switched to trajectory trace mode")
    
    def set_force_view(self):
        """Set force vector visualization mode."""
        self.visualization.set_visualization_mode(VisualizationMode.FORCE_VECTORS)
        self.update_status("Visualization: Force vectors mode")
        self.error_handler.show_info_message("Visualization", "Switched to force vectors mode")
    
    def reset_camera(self):
        """Reset camera to default position."""
        self.visualization.set_camera_position()
        self.update_status("Camera reset to default position")
        self.error_handler.show_success_message("Camera", "Reset to default position")
    
    # Export/Save methods
    def save_session(self):
        """Save current session."""
        session_id = self.export_system.save_session(self.demo_scene, session_name="demo")
        if session_id:
            self.update_status(f"Session saved: {session_id}")
            self.error_handler.show_success_message("Save", f"Session saved as {session_id}")
        else:
            self.error_handler.show_warning_message("Save", "Failed to save session")
    
    def load_session(self):
        """Load a session."""
        sessions = self.export_system.list_saved_sessions()
        if sessions:
            latest_session = sessions[0]['session_id']
            session_data = self.export_system.load_session(latest_session)
            if session_data:
                self.update_status(f"Loaded session: {latest_session}")
                self.error_handler.show_success_message("Load", f"Loaded session {latest_session}")
            else:
                self.error_handler.show_warning_message("Load", "Failed to load session")
        else:
            self.error_handler.show_info_message("Load", "No saved sessions found")
    
    def export_scene(self):
        """Export current scene."""
        export_path = f"demo_export_{int(time.time())}.json"
        settings = ExportSettings(format="json")
        success = self.export_system.export_scene_data(self.demo_scene, export_path, settings)
        
        if success:
            self.update_status(f"Scene exported to {export_path}")
            self.error_handler.show_success_message("Export", f"Scene exported to {export_path}")
        else:
            self.error_handler.show_warning_message("Export", "Failed to export scene")
    
    def list_sessions(self):
        """List all saved sessions."""
        sessions = self.export_system.list_saved_sessions()
        session_info = f"Found {len(sessions)} saved sessions"
        
        if sessions:
            session_info += f"\nLatest: {sessions[0]['session_id']}"
        
        self.update_status(session_info)
        self.error_handler.show_info_message("Sessions", session_info)
    
    # Performance monitoring methods
    def start_monitoring(self):
        """Start performance monitoring."""
        self.performance_monitor.start_monitoring()
        self.update_status("Performance monitoring started")
        self.error_handler.show_success_message("Performance", "Monitoring started")
    
    def stop_monitoring(self):
        """Stop performance monitoring."""
        self.performance_monitor.stop_monitoring()
        self.update_status("Performance monitoring stopped")
        self.error_handler.show_info_message("Performance", "Monitoring stopped")
    
    def show_performance_stats(self):
        """Show performance statistics."""
        stats = self.performance_monitor.get_performance_stats()
        dashboard_data = self.performance_monitor.get_dashboard_data()
        
        stats_info = f"CPU: {dashboard_data['current']['cpu']}, Memory: {dashboard_data['current']['memory']}, Status: {dashboard_data['status']}"
        self.update_status(f"Performance: {stats_info}")
        self.error_handler.show_info_message("Performance Stats", stats_info)
    
    def generate_performance_report(self):
        """Generate performance report."""
        report = self.performance_monitor.generate_performance_report()
        
        report_summary = f"Uptime: {report['performance_summary']['uptime_hours']}h, "
        report_summary += f"Avg CPU: {report['performance_summary']['avg_cpu_usage']}%, "
        report_summary += f"Warnings: {len(report['warnings'])}"
        
        self.update_status(f"Performance Report: {report_summary}")
        self.error_handler.show_info_message("Performance Report", report_summary)
    
    # Error handling test methods
    def test_success_message(self):
        """Test success message."""
        self.error_handler.show_success_message("Success Test", "This is a success message!")
        self.update_status("Displayed success message")
    
    def test_warning(self):
        """Test warning message."""
        try:
            # Simulate a warning condition
            raise ValueError("This is a test warning")
        except Exception as e:
            self.error_handler.handle_error(e, context={'test': 'warning'})
    
    def test_error(self):
        """Test error message."""
        try:
            # Simulate an error
            result = 1 / 0
        except Exception as e:
            self.error_handler.handle_error(e, context={'test': 'error'})
    
    def test_loading(self):
        """Test loading indicator."""
        indicator_id = self.error_handler.create_loading_indicator(self.root, "Testing loading...")
        
        def remove_loading():
            time.sleep(2)
            self.error_handler.remove_loading_indicator(indicator_id)
            self.update_status("Loading test completed")
        
        import threading
        threading.Thread(target=remove_loading, daemon=True).start()
    
    def test_progress(self):
        """Test progress bar."""
        progress_id = self.error_handler.create_progress_bar(self.root, "Testing progress...")
        
        def update_progress():
            for i in range(101):
                self.error_handler.update_progress(progress_id, i, f"Progress test: {i}%")
                time.sleep(0.02)
            self.error_handler.remove_progress_bar(progress_id)
            self.update_status("Progress test completed")
        
        import threading
        threading.Thread(target=update_progress, daemon=True).start()
    
    # Demo utility methods
    def show_shortcuts_help(self):
        """Show keyboard shortcuts help."""
        self.shortcut_manager.show_help_dialog()
    
    def toggle_demo(self):
        """Toggle demo simulation."""
        self.demo_running = not self.demo_running
        status = "started" if self.demo_running else "stopped"
        self.update_status(f"Demo simulation {status}")
        self.error_handler.show_info_message("Demo", f"Simulation {status}")
    
    def reset_demo(self):
        """Reset demo state."""
        self.demo_scene = DynamicPhysicsScene("week9_demo_reset")
        self.demo_running = False
        self.update_status("Demo reset")
        self.error_handler.show_success_message("Demo", "Demo state reset")
    
    def toggle_debug_mode(self):
        """Toggle debug mode."""
        self.set_debug_view()
    
    def create_demo_ball(self):
        """Create demo ball."""
        ball = DynamicPhysicsObject(
            object_id=f"demo_ball_{int(time.time())}",
            object_type=ObjectType.SPHERE,
            position=Vector3(0, 0, 2),
            material=MaterialType.RUBBER
        )
        self.demo_scene.add_object(ball)
        self.update_status(f"Created demo ball: {ball.object_id}")
        self.error_handler.show_success_message("Create", "Demo ball created")
    
    def create_demo_box(self):
        """Create demo box."""
        box = DynamicPhysicsObject(
            object_id=f"demo_box_{int(time.time())}",
            object_type=ObjectType.BOX,
            position=Vector3(1, 0, 1),
            material=MaterialType.WOOD
        )
        self.demo_scene.add_object(box)
        self.update_status(f"Created demo box: {box.object_id}")
        self.error_handler.show_success_message("Create", "Demo box created")
    
    def run_demo(self):
        """Run the demo."""
        print("🎬 Starting Week 9 UI Polish & Advanced Features Demo...")
        print("🎯 Try the various features and keyboard shortcuts!")
        
        # Start performance monitoring
        self.start_monitoring()
        
        # Show initial help
        self.error_handler.show_info_message(
            "Welcome to Week 9 Demo!",
            "Try the buttons or press F1 for keyboard shortcuts help"
        )
        
        # Run the demo
        self.root.mainloop()
        
        # Cleanup
        self.stop_monitoring()
        print("🎉 Week 9 demo completed!")


def main():
    """Run the Week 9 UI polish and advanced features demo."""
    demo = Week9UIPolishDemo()
    demo.run_demo()


if __name__ == "__main__":
    main()
