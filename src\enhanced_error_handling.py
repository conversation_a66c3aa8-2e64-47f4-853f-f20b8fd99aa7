"""
Enhanced Error Handling and User Feedback
Improves error messages, loading indicators, and user feedback systems
while maintaining the current UI design.
"""

import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
import traceback
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import logging


class FeedbackType(Enum):
    """Types of user feedback."""
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    INFO = "info"
    LOADING = "loading"
    PROGRESS = "progress"


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ErrorInfo:
    """Information about an error."""
    error_type: str
    message: str
    severity: ErrorSeverity
    timestamp: float
    context: Dict[str, Any]
    traceback_info: Optional[str] = None
    user_action: Optional[str] = None
    suggested_fix: Optional[str] = None


@dataclass
class FeedbackMessage:
    """User feedback message."""
    message_type: FeedbackType
    title: str
    content: str
    duration: float = 3.0
    actions: List[Dict[str, Any]] = None


class EnhancedErrorHandler:
    """Enhanced error handling system with user-friendly feedback."""
    
    def __init__(self, parent_widget: tk.Widget = None):
        """Initialize enhanced error handler."""
        self.parent = parent_widget
        self.error_history = []
        self.feedback_widgets = {}
        
        # Setup logging
        self.setup_logging()
        
        # Error message templates
        self.error_templates = self._build_error_templates()
        
        # Loading indicators
        self.loading_widgets = {}
        self.progress_bars = {}
        
        print("🛡️ Enhanced error handling system initialized")
    
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('physics_app.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _build_error_templates(self) -> Dict[str, Dict[str, str]]:
        """Build user-friendly error message templates."""
        return {
            'file_not_found': {
                'title': 'File Not Found',
                'message': 'The file "{filename}" could not be found.',
                'suggestion': 'Please check the file path and try again.',
                'severity': 'medium'
            },
            'invalid_command': {
                'title': 'Invalid Command',
                'message': 'The command "{command}" is not recognized.',
                'suggestion': 'Try using simpler language or check the help for available commands.',
                'severity': 'low'
            },
            'physics_error': {
                'title': 'Physics Simulation Error',
                'message': 'An error occurred during physics simulation: {details}',
                'suggestion': 'Try resetting the simulation or simplifying the scene.',
                'severity': 'medium'
            },
            'ml_model_error': {
                'title': 'AI Model Error',
                'message': 'The AI model encountered an error: {details}',
                'suggestion': 'The model may need retraining or the input may be too complex.',
                'severity': 'high'
            },
            'memory_error': {
                'title': 'Memory Error',
                'message': 'The application is running low on memory.',
                'suggestion': 'Try closing other applications or simplifying the current scene.',
                'severity': 'high'
            },
            'network_error': {
                'title': 'Network Error',
                'message': 'Network connection failed: {details}',
                'suggestion': 'Check your internet connection and try again.',
                'severity': 'medium'
            },
            'validation_error': {
                'title': 'Input Validation Error',
                'message': 'Invalid input: {details}',
                'suggestion': 'Please check your input and ensure it meets the requirements.',
                'severity': 'low'
            }
        }
    
    def handle_error(self, error: Exception, context: Dict[str, Any] = None, 
                    user_action: str = None) -> ErrorInfo:
        """Handle an error with enhanced user feedback."""
        # Determine error type and severity
        error_type = type(error).__name__
        severity = self._determine_severity(error)
        
        # Create error info
        error_info = ErrorInfo(
            error_type=error_type,
            message=str(error),
            severity=severity,
            timestamp=time.time(),
            context=context or {},
            traceback_info=traceback.format_exc(),
            user_action=user_action
        )
        
        # Generate user-friendly message
        user_message = self._generate_user_message(error_info)
        error_info.suggested_fix = user_message.get('suggestion', '')
        
        # Log error
        self.logger.error(f"{error_type}: {error_info.message}", exc_info=True)
        
        # Store in history
        self.error_history.append(error_info)
        
        # Show user feedback
        self._show_error_feedback(error_info, user_message)
        
        return error_info
    
    def _determine_severity(self, error: Exception) -> ErrorSeverity:
        """Determine error severity based on error type."""
        critical_errors = [SystemExit, KeyboardInterrupt, MemoryError]
        high_errors = [ImportError, ModuleNotFoundError, AttributeError]
        medium_errors = [FileNotFoundError, ValueError, TypeError]
        
        error_type = type(error)
        
        if any(isinstance(error, err_type) for err_type in critical_errors):
            return ErrorSeverity.CRITICAL
        elif any(isinstance(error, err_type) for err_type in high_errors):
            return ErrorSeverity.HIGH
        elif any(isinstance(error, err_type) for err_type in medium_errors):
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _generate_user_message(self, error_info: ErrorInfo) -> Dict[str, str]:
        """Generate user-friendly error message."""
        error_type = error_info.error_type.lower()
        
        # Check for specific error templates
        for template_key, template in self.error_templates.items():
            if template_key in error_type or template_key in error_info.message.lower():
                return {
                    'title': template['title'],
                    'message': template['message'].format(
                        details=error_info.message,
                        **error_info.context
                    ),
                    'suggestion': template['suggestion']
                }
        
        # Default message
        return {
            'title': f'{error_info.error_type} Error',
            'message': error_info.message,
            'suggestion': 'Please try again or contact support if the problem persists.'
        }
    
    def _show_error_feedback(self, error_info: ErrorInfo, user_message: Dict[str, str]):
        """Show error feedback to user."""
        if error_info.severity == ErrorSeverity.CRITICAL:
            self._show_critical_error_dialog(user_message)
        elif error_info.severity == ErrorSeverity.HIGH:
            self._show_error_dialog(user_message)
        elif error_info.severity == ErrorSeverity.MEDIUM:
            self._show_warning_notification(user_message)
        else:
            self._show_info_notification(user_message)
    
    def _show_critical_error_dialog(self, message: Dict[str, str]):
        """Show critical error dialog."""
        if self.parent:
            messagebox.showerror(
                title=message['title'],
                message=f"{message['message']}\n\n{message['suggestion']}"
            )
    
    def _show_error_dialog(self, message: Dict[str, str]):
        """Show error dialog."""
        if self.parent:
            messagebox.showerror(
                title=message['title'],
                message=f"{message['message']}\n\n{message['suggestion']}"
            )
    
    def _show_warning_notification(self, message: Dict[str, str]):
        """Show warning notification."""
        if self.parent:
            messagebox.showwarning(
                title=message['title'],
                message=f"{message['message']}\n\n{message['suggestion']}"
            )
    
    def _show_info_notification(self, message: Dict[str, str]):
        """Show info notification."""
        if self.parent:
            messagebox.showinfo(
                title=message['title'],
                message=f"{message['message']}\n\n{message['suggestion']}"
            )
    
    def show_success_message(self, title: str, message: str, duration: float = 3.0):
        """Show success message."""
        self._show_temporary_notification(title, message, FeedbackType.SUCCESS, duration)
    
    def show_info_message(self, title: str, message: str, duration: float = 3.0):
        """Show info message."""
        self._show_temporary_notification(title, message, FeedbackType.INFO, duration)
    
    def _show_temporary_notification(self, title: str, message: str, 
                                   feedback_type: FeedbackType, duration: float):
        """Show temporary notification that auto-dismisses."""
        if not self.parent:
            print(f"{feedback_type.value.upper()}: {title} - {message}")
            return
        
        # Create notification window
        notification = tk.Toplevel(self.parent)
        notification.title(title)
        notification.geometry("300x100")
        notification.resizable(False, False)
        
        # Configure colors based on type
        colors = {
            FeedbackType.SUCCESS: {'bg': '#d4edda', 'fg': '#155724'},
            FeedbackType.WARNING: {'bg': '#fff3cd', 'fg': '#856404'},
            FeedbackType.ERROR: {'bg': '#f8d7da', 'fg': '#721c24'},
            FeedbackType.INFO: {'bg': '#d1ecf1', 'fg': '#0c5460'}
        }
        
        color_config = colors.get(feedback_type, colors[FeedbackType.INFO])
        notification.configure(bg=color_config['bg'])
        
        # Add message
        label = tk.Label(
            notification,
            text=message,
            bg=color_config['bg'],
            fg=color_config['fg'],
            wraplength=280,
            justify=tk.CENTER,
            font=('Arial', 10)
        )
        label.pack(expand=True, fill=tk.BOTH, padx=10, pady=10)
        
        # Position notification
        notification.transient(self.parent)
        notification.grab_set()
        
        # Auto-dismiss after duration
        def auto_dismiss():
            time.sleep(duration)
            if notification.winfo_exists():
                notification.destroy()
        
        threading.Thread(target=auto_dismiss, daemon=True).start()
    
    def create_loading_indicator(self, parent: tk.Widget, message: str = "Loading...") -> str:
        """Create loading indicator."""
        indicator_id = f"loading_{int(time.time() * 1000)}"
        
        # Create loading frame
        loading_frame = tk.Frame(parent, bg='lightgray', relief=tk.RAISED, bd=2)
        loading_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # Add loading message
        loading_label = tk.Label(
            loading_frame,
            text=message,
            bg='lightgray',
            font=('Arial', 9)
        )
        loading_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # Add progress bar
        progress = ttk.Progressbar(
            loading_frame,
            mode='indeterminate',
            length=200
        )
        progress.pack(side=tk.RIGHT, padx=10, pady=5)
        progress.start()
        
        # Store references
        self.loading_widgets[indicator_id] = {
            'frame': loading_frame,
            'label': loading_label,
            'progress': progress
        }
        
        return indicator_id
    
    def update_loading_message(self, indicator_id: str, message: str):
        """Update loading indicator message."""
        if indicator_id in self.loading_widgets:
            self.loading_widgets[indicator_id]['label'].config(text=message)
    
    def remove_loading_indicator(self, indicator_id: str):
        """Remove loading indicator."""
        if indicator_id in self.loading_widgets:
            widgets = self.loading_widgets[indicator_id]
            widgets['progress'].stop()
            widgets['frame'].destroy()
            del self.loading_widgets[indicator_id]
    
    def create_progress_bar(self, parent: tk.Widget, title: str = "Progress") -> str:
        """Create progress bar for long operations."""
        progress_id = f"progress_{int(time.time() * 1000)}"
        
        # Create progress frame
        progress_frame = tk.Frame(parent, bg='white', relief=tk.RAISED, bd=2)
        progress_frame.pack(fill=tk.X, padx=5, pady=2)
        
        # Add title
        title_label = tk.Label(
            progress_frame,
            text=title,
            bg='white',
            font=('Arial', 9, 'bold')
        )
        title_label.pack(anchor=tk.W, padx=10, pady=(5, 0))
        
        # Add progress bar
        progress = ttk.Progressbar(
            progress_frame,
            mode='determinate',
            length=300
        )
        progress.pack(fill=tk.X, padx=10, pady=5)
        
        # Add percentage label
        percent_label = tk.Label(
            progress_frame,
            text="0%",
            bg='white',
            font=('Arial', 8)
        )
        percent_label.pack(anchor=tk.E, padx=10, pady=(0, 5))
        
        # Store references
        self.progress_bars[progress_id] = {
            'frame': progress_frame,
            'title': title_label,
            'progress': progress,
            'percent': percent_label
        }
        
        return progress_id
    
    def update_progress(self, progress_id: str, value: float, message: str = None):
        """Update progress bar value (0-100)."""
        if progress_id in self.progress_bars:
            widgets = self.progress_bars[progress_id]
            widgets['progress']['value'] = value
            widgets['percent'].config(text=f"{value:.1f}%")
            
            if message:
                widgets['title'].config(text=message)
    
    def remove_progress_bar(self, progress_id: str):
        """Remove progress bar."""
        if progress_id in self.progress_bars:
            self.progress_bars[progress_id]['frame'].destroy()
            del self.progress_bars[progress_id]
    
    def safe_execute(self, func: Callable, *args, **kwargs) -> Any:
        """Safely execute function with error handling."""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.handle_error(e, context={'function': func.__name__, 'args': args, 'kwargs': kwargs})
            return None
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get summary of recent errors."""
        recent_errors = [e for e in self.error_history if time.time() - e.timestamp < 3600]  # Last hour
        
        severity_counts = {}
        for severity in ErrorSeverity:
            severity_counts[severity.value] = len([e for e in recent_errors if e.severity == severity])
        
        return {
            'total_errors': len(self.error_history),
            'recent_errors': len(recent_errors),
            'severity_breakdown': severity_counts,
            'most_common_errors': self._get_most_common_errors(recent_errors)
        }
    
    def _get_most_common_errors(self, errors: List[ErrorInfo]) -> List[Dict[str, Any]]:
        """Get most common error types."""
        error_counts = {}
        for error in errors:
            error_type = error.error_type
            if error_type not in error_counts:
                error_counts[error_type] = 0
            error_counts[error_type] += 1
        
        # Sort by frequency
        sorted_errors = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)
        
        return [{'type': error_type, 'count': count} for error_type, count in sorted_errors[:5]]


def test_enhanced_error_handling():
    """Test the enhanced error handling system."""
    print("Testing Enhanced Error Handling System...")
    
    # Create test Tkinter window
    root = tk.Tk()
    root.title("Error Handling Test")
    root.geometry("500x400")
    
    # Create error handler
    error_handler = EnhancedErrorHandler(root)
    
    # Test different types of errors
    def test_file_error():
        try:
            with open('nonexistent_file.txt', 'r') as f:
                content = f.read()
        except Exception as e:
            error_handler.handle_error(e, context={'filename': 'nonexistent_file.txt'})
    
    def test_value_error():
        try:
            result = int("not_a_number")
        except Exception as e:
            error_handler.handle_error(e, context={'input': 'not_a_number'})
    
    def test_success_message():
        error_handler.show_success_message("Success!", "Operation completed successfully")
    
    def test_loading_indicator():
        indicator_id = error_handler.create_loading_indicator(root, "Processing...")
        
        def remove_after_delay():
            time.sleep(3)
            error_handler.remove_loading_indicator(indicator_id)
        
        threading.Thread(target=remove_after_delay, daemon=True).start()
    
    def test_progress_bar():
        progress_id = error_handler.create_progress_bar(root, "Training Model")
        
        def update_progress():
            for i in range(101):
                error_handler.update_progress(progress_id, i, f"Training... Step {i}/100")
                time.sleep(0.05)
            error_handler.remove_progress_bar(progress_id)
        
        threading.Thread(target=update_progress, daemon=True).start()
    
    # Create test buttons
    button_frame = tk.Frame(root)
    button_frame.pack(pady=20)
    
    tk.Button(button_frame, text="Test File Error", command=test_file_error).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Test Value Error", command=test_value_error).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Test Success", command=test_success_message).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Test Loading", command=test_loading_indicator).pack(side=tk.LEFT, padx=5)
    tk.Button(button_frame, text="Test Progress", command=test_progress_bar).pack(side=tk.LEFT, padx=5)
    
    # Test error summary
    summary = error_handler.get_error_summary()
    print(f"✅ Error summary: {summary}")
    
    print("✅ Enhanced error handling test setup complete!")
    print("🎯 Try the test buttons to see error handling in action")
    
    # Don't run mainloop in test
    root.destroy()
    
    print("✅ Enhanced error handling test completed!")


if __name__ == "__main__":
    test_enhanced_error_handling()
