example_id,text_description,num_objects,scenario_type,complexity,tags,has_ramp,has_sphere,has_box
example_8baf670b,make a incline and set a 0.6kg rubber round object at the top so it will move down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_15a402ee,place a 2.6kg round object in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_55b9fbdb,insert a rubber orb that will rebound off a bouncy box,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_3af1e57a,construct a scenario where a 1.4kg round object rolls down a wooden ramp and collides with a box,7,complex,complex,"complex,multi-object,interaction",True,True,True
example_251ee61a,introduce a iron orb,2,collision,simple,"collision,impact,momentum",False,True,False
example_5147a2a1,position a 3.9kg round object in the scene,4,complex,complex,"complex,multi-object,interaction",True,True,False
example_b798895d,introduce a steel ball and a wooden slope,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_47b140a3,put a 1.0kg orb in the scene,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_f1d8fef9,drop a 2.8kg orb on top of a incline,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_6de19173,insert a marble block,1,drop,simple,"drop,gravity,falling",False,False,True
example_76b14ed4,insert a polymer sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_70a2ff2d,Build a slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_377e5955,place a 2.4kg sphere in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_f89bd629,build a ball,2,collision,simple,"collision,impact,momentum",False,True,False
example_44560968,drop a 3.1kg sphere in the scene,3,drop,complex,"drop,gravity,falling",False,True,True
example_22f119e9,add a wooden ball that will rebound off a super elastic box,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_413d4dba,form a sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_0ac65e2a,make a orb,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_89ddd645,include a stone round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_f0107214,drop a 2.1kg round object in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_baf53877,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_cbf051a8,set a 1.1kg round object in the scene,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_83553d93,create a sphere,1,collision,simple,"collision,impact,momentum",False,True,False
example_467862e1,include a bouncy ball,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_89b85da7,build a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_376ea606,include a plastic cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_dbc156c7,create a block and a round object next to each other,2,drop,simple,"drop,gravity,falling",False,True,True
example_16750427,make a ball,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_a87806b5,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_536f331c,Set up a ramp with a sphere that will roll down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_ac024407,insert a highly elastic sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_bf555028,put a 1.1kg round object in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_06a8701c,place a 0.7kg round object in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_3e3337a7,position a 0.8kg ball on top of a ramp,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_8275f663,build a ramp and put a sphere on it,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_505aa297,Set up a inclined plane with a orb that will roll down,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_ff343ae9,Set up a ramp with a sphere that will move down,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_fa55e82a,position a 3.6kg orb in the scene,2,drop,simple,"drop,gravity,falling",False,True,True
example_663a55b2,set a 1.1kg sphere in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_5047f53b,include a elastic ball,2,collision,simple,"collision,impact,momentum",False,True,False
example_c204000a,construct a round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_45c1f885,insert a wood sphere and a timber inclined plane,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_a3a9be48,insert a elastic round object,2,collision,simple,"collision,impact,momentum",False,True,False
example_ff206c32,include a rubber ball,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_95b37f65,make a sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_39b00995,put a 0.4kg orb in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_eba445cb,include a super elastic ball,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_70b94dce,create a orb,2,drop,simple,"drop,gravity,falling",False,True,False
example_f62b1f42,make a incline and position a 2.2kg steel sphere at the top so it will roll down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_151ec00b,insert a wood round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_9a4f2d26,add a bouncy sphere,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_b8c4f717,build a sphere,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_77815101,insert a bouncy round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_386f3fda,drop a 2.0kg ball in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_b6455cf1,Create an empty physics scene,0,drop,simple,"drop,gravity,falling",False,False,False
example_f1a2c53f,construct a orb,2,drop,simple,"drop,gravity,falling",False,True,False
example_e28a10c3,form a incline and set a 1.9kg timber round object at the top so it will slide down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_62a0695f,drop a 1.9kg ball in the scene,1,drop,simple,"drop,gravity,falling",False,True,False
example_281475cd,position a 0.7kg round object in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_923dfe42,introduce a wooden round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_c145bb4b,create a orb,1,drop,simple,"drop,gravity,falling",False,True,False
example_1d6195b5,set a 1.6kg orb in the scene,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_216de4db,insert a plastic box,3,drop,complex,"drop,gravity,falling",False,True,True
example_84a67f01,construct a inclined plane and set a 1.5kg polymer sphere at the top so it will roll down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_49625df8,build a round object,1,drop,simple,"drop,gravity,falling",False,True,False
example_0490832c,construct a orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_3ae1a539,build a sphere,5,complex,complex,"complex,multi-object,interaction",True,True,False
example_7b096a99,put a 1.0kg sphere in the scene,1,collision,simple,"collision,impact,momentum",False,True,False
example_d6af387f,Set up a incline with a orb that will slide down,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_ed1a02eb,set a 0.6kg sphere on top of a slope,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_9d78684b,place a 1.1kg orb in the scene,2,collision,simple,"collision,impact,momentum",False,True,False
example_a698e8d1,include a wood sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_3b0a0dc0,add a bouncy round object,2,collision,simple,"collision,impact,momentum",False,True,False
example_9eb6c81c,place a 2.1kg ball in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_cc0c8e69,form a sphere,7,complex,complex,"complex,multi-object,interaction",True,True,False
example_b6393ba3,put a 1.5kg round object in the scene,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_a8098121,introduce a metal block,2,drop,simple,"drop,gravity,falling",False,False,True
example_d4da37ea,Build a inclined plane,4,complex,complex,"complex,multi-object,interaction",True,True,False
example_fda1c1dd,build a orb,1,collision,simple,"collision,impact,momentum",False,True,False
example_56f5c702,drop a 2.0kg orb in the scene,6,complex,complex,"complex,multi-object,interaction",True,True,True
example_f2026f46,Build a ramp,1,ramp,simple,"ramp,rolling,incline,gravity",True,False,False
example_5351b5ed,make a round object,2,collision,simple,"collision,impact,momentum",False,True,False
example_ff3e3b29,introduce a bouncy ball,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_cccc282f,make a ball,1,collision,simple,"collision,impact,momentum",False,True,False
example_e9003ffa,add a rubber round object,2,collision,simple,"collision,impact,momentum",False,True,False
example_826d3327,make a ramp and drop a 2.2kg marble sphere at the top so it will move down due to gravity,2,ramp,simple,"ramp,rolling,incline,gravity",True,True,False
example_9bec0395,introduce a steel ball that will bounce off a metallic box,5,complex,complex,"complex,multi-object,interaction",True,True,True
example_5e53fa78,insert a rubber orb,3,bouncing,complex,"bouncing,elastic,restitution",False,True,False
example_c03c74c1,make a orb,2,collision,simple,"collision,impact,momentum",False,True,False
example_044e2f21,set a 1.0kg orb in the scene,2,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_3d210b0a,build a rectangular object,4,complex,complex,"complex,multi-object,interaction",True,True,True
example_b2e1fe59,insert a polymer round object,1,collision,simple,"collision,impact,momentum",False,True,False
example_35c43312,add a bouncy cube,1,drop,simple,"drop,gravity,falling",False,False,True
example_7aeb0794,include a super elastic round object,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_40a1af9f,include a rubber orb,1,bouncing,simple,"bouncing,elastic,restitution",False,True,False
example_ee1d8599,construct a ball,1,drop,simple,"drop,gravity,falling",False,True,False
example_dfd96b1c,create a sphere,2,collision,simple,"collision,impact,momentum",False,True,False
example_0c7d9cf5,introduce a rocky ball,3,ramp,complex,"ramp,rolling,incline,gravity",True,True,False
example_c87f472a,construct a sphere,1,drop,simple,"drop,gravity,falling",False,True,False
example_db0d65ad,form a cube,1,drop,simple,"drop,gravity,falling",False,False,True
